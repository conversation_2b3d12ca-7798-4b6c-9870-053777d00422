/*
 * @(#)SweepSearchAction.java 1.0 12/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;






import org.swallow.control.model.Archive;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.RoleTO;
import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.*;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.Movement;
import org.swallow.work.model.Sweep;
import org.swallow.work.service.ILMAnalysisMonitorManager;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.MovementSearchManager;
import org.swallow.work.service.SweepSearchManager;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;
import org.swallow.util.struts.CustomActionSupport;

/**
 * This class is used to search the sweep details
 * 
 */
@Action(value = "/sweepsearch", results = {
	@Result(name = "fail", location = "/error.jsp"),
	@Result(name = "success", location = "/jsp/work/sweepsearch.jsp"),
	@Result(name = "search", location = "/jsp/work/sweepsearchlist.jsp"),
	@Result(name = "SweepGetGrid", location = "/jsp/work/sweepsearchlistgetgrid.jsp"),
	@Result(name = "data", location = "/jsp/data.jsp"),
	@Result(name = "view", location = "/jsp/work/sweepdisplay.jsp"),
	@Result(name = "archiveSweep", location = "/jsp/work/archivesweep.jsp"),
})

@AllowedMethods ({"display" ,"archiveSweep" ,"displaysearch" ,"search" ,"view" ,"populate" ,"checkSweepId" ,"SweepGetGrid" ,"getSweepGrid" ,"OpenSubSweepSearchList" })
public class SweepSearchAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        case "unspecified":
            return unspecified();
        case "display":
            return display();
        case "archiveSweep":
            return archiveSweep();
        case "displaysearch":
            return displaysearch();
        case "search":
            return search();
        case "view":
            return view();
        case "populate":
            return populate();
        case "checkSweepId":
            return checkSweepId();
        case "SweepGetGrid":
            return SweepGetGrid();
        case "getSweepGrid":
            return getSweepGrid();
        case "OpenSubSweepSearchList":
            return OpenSubSweepSearchList();
        default:
            break;
    }

    return unspecified();
}



	private final Log log = LogFactory.getLog(SweepSearchAction.class);
	@Autowired
	private SweepSearchManager sweepsearchManager;

	public void setSweepsearchManager(SweepSearchManager sweepsearchManager) {
		this.sweepsearchManager = sweepsearchManager;
	}

	public String unspecified()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		log.debug("exiting 'unspecified' method");

		return display();
	}

	
	private Sweep sweepsearch;
	public Sweep getSweepsearch() {
		if (sweepsearch == null) {
			sweepsearch = new Sweep();
		}
		return sweepsearch;
	}
	public void setSweepsearch(Sweep sweepsearch) {
		this.sweepsearch = sweepsearch;
		HttpServletRequest request = ServletActionContext.getRequest();
		request.setAttribute("sweepsearch", sweepsearch);
	}

	
	public String display()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		Calendar cal = null;
		Date date;
		String valueDate = null;
		String fromArchive = null;
		try {
			String currencyGroup = "";

			String roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();

			log.debug("entering 'display' method");

			// DynaValidatorForm Sweep sweepsearch = (Sweep) (getSweepsearch());
			Sweep sweepsearch = (Sweep) (getSweepsearch());
			fromArchive = request.getParameter("fromArchiveScreen");
			String entityId = sweepsearch.getEntityId();

			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				sweepsearch.setEntityId(entityId);
			}

			putEntityListInReq(request);

			log.debug("entityId:::::" + entityId);
			if(SwtUtil.isEmptyOrNull(sweepsearch.getValueFromDateAsString())){
				cal = Calendar.getInstance();
				cal.setTime(SwtUtil.getSysParamDateWithEntityOffset(entityId));
				date = cal.getTime();
				/* Format the date for Current System Formats */
				valueDate = SwtUtil.formatDate(date, SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue());

				sweepsearch.setValueFromDateAsString(valueDate);
			}

			// Initializing all values to null
			request.setAttribute("currencydetails", new ArrayList());
			request.setAttribute("accountdetails", new ArrayList());
			request.setAttribute("gendetails", new ArrayList());
			request.setAttribute("subdetails", new ArrayList());
			request.setAttribute("authdetails", new ArrayList());
			request.setAttribute("msgdetails", new ArrayList());

			// fetching the hostid
			String hostId = CacheManager.getInstance().getHostId();

			// Populating the currency drop down
			Collection currlist = new ArrayList();
			Collection currlistLvl = new ArrayList();
			currlistLvl.add(new LabelValueBean("", ""));

			currencyGroup = ((request.getParameter("selectedCurrencyGroup") != null) && (request
					.getParameter("selectedCurrencyGroup").trim().length() > 0)) ? request
					.getParameter("selectedCurrencyGroup")
					: "All";

			if (!currencyGroup.equalsIgnoreCase("All")) {
				currlist = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getCurrencies(
								new EntityCurrencyGroupTO(entityId,
										currencyGroup));
				if ((currlist != null) && (currlist.size() > 0)) {
					Iterator it = currlist.iterator();

					while (it.hasNext()) {
						CurrencyTO currencyTo = (CurrencyTO) it.next();
						currlistLvl
								.add(new LabelValueBean(currencyTo
										.getCurrencyName(), currencyTo
										.getCurrencyId()));
					}
				}

				if (currlistLvl != null) {
					currlistLvl.remove(new LabelValueBean("Default", "*"));
				}

				request.setAttribute("currencydetails", currlistLvl);
			} else {

				MovementSearchManager mgr = (MovementSearchManager) (SwtUtil
						.getBean("movementsearchManager"));

				currlist = getCurrencyList(request, hostId, entityId);

				request.setAttribute("currencydetails", currlist);

			}

			log.debug("Outside currencyList");

			String currencyCode = ((sweepsearch.getCurrencyCode() != null) && (sweepsearch
					.getCurrencyCode().trim().length() > 0)) ? sweepsearch
					.getCurrencyCode().trim() : "%";

			if (currencyCode != null && currencyCode.equals("All")) {
				currencyCode = "%";
			}

			// Populating the account combo box
			Collection accountlist = sweepsearchManager.getAccountDetails(
					hostId, entityId, currencyCode);
			request.setAttribute("accountdetails", accountlist);

			// Populating the generated by combo box
			Collection genlist = sweepsearchManager.getGeneratedDetails(hostId,
					entityId);
			request.setAttribute("gendetails", genlist);

			// Populating the submitted by combo box
			Collection sublist = sweepsearchManager.getSubmittedDetails(hostId,
					entityId);
			request.setAttribute("subdetails", sublist);

			// Populating the authorized by combo box
			Collection authlist = sweepsearchManager.getAuthorizedDetails(
					hostId, entityId);
			request.setAttribute("authdetails", authlist);

			// Populating the message combo box
			Collection msglist = sweepsearchManager.getMessageDetails(hostId,
					entityId);
			request.setAttribute("msgdetails", msglist);

			// Populating the bookcode combo box
			Collection booklist = sweepsearchManager.getBookCodeDetails(hostId,
					entityId);

			request.setAttribute("bookdetails", booklist);

			putCurrencyGroupListInReq(request, hostId, entityId, false);

			// set the preSystemFromDateAsString to request attribute
			if (request.getParameter("preSystemFromDateAsString") != null) {
				request.setAttribute("preSystemFromDateAsString",valueDate);
			}
			// set the preAmountover to request attribute
			if (request.getParameter("preAmountover") != null) {
				request.setAttribute("preAmountover", request
						.getParameter("preAmountover"));
			}
			// set the preAmountunder to request attribute
			if (request.getParameter("preAmountunder") != null) {
				request.setAttribute("preAmountunder", request
						.getParameter("preAmountunder"));
			}
			String parentSrc=request.getParameter("parentScr");
                         if(parentSrc!=null)
			request.setAttribute("parentScreen", parentSrc);
                        String acctType = request.getParameter("acctType");
			if(acctType!=null)
			request.setAttribute("acctType", acctType);
			request.setAttribute("methodName", "search");

			Collection coll = (Collection) request
					.getAttribute("currencydetails");

			log.debug("exiting 'display' method");
			if ("true".equals(fromArchive)) {
				putArchiveListinRequest(request);
				return ("archiveSweep");
			}
			return ("success");
		} catch (SwtException swtexp) {
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception e) {
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", SweepSearchAction.class), request, "");

			return ("fail");
		}
	}
	public String archiveSweep()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		Calendar cal = null;
		Date date;
		String valueDate = null;

		try {
			String currencyGroup = "";

			String roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();

			log.debug("entering 'display' method");

//			 DynaValidatorForm Sweep sweepsearch = (Sweep) (getSweepsearch());
			Sweep sweepsearch = (Sweep) (getSweepsearch());
			String entityId = sweepsearch.getEntityId();

			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				sweepsearch.setEntityId(entityId);
			}

			putEntityListInReq(request);
			putArchiveListinRequest(request);
			log.debug("entityId:::::" + entityId);

			cal = Calendar.getInstance();
			cal.setTime(SwtUtil.getSysParamDateWithEntityOffset(entityId));
			date = cal.getTime();
			/* Format the date for Current System Formats */
			valueDate = SwtUtil.formatDate(date, SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			sweepsearch.setValueFromDateAsString(valueDate);

			// Initializing all values to null
			request.setAttribute("currencydetails", new ArrayList());
			request.setAttribute("accountdetails", new ArrayList());
			request.setAttribute("gendetails", new ArrayList());
			request.setAttribute("subdetails", new ArrayList());
			request.setAttribute("authdetails", new ArrayList());
			request.setAttribute("msgdetails", new ArrayList());

			// fetching the hostid
			String hostId = CacheManager.getInstance().getHostId();

			// Populating the currency drop down
			Collection currlist = new ArrayList();
			Collection currlistLvl = new ArrayList();
			currlistLvl.add(new LabelValueBean("", ""));

			currencyGroup = ((request.getParameter("selectedCurrencyGroup") != null) && (request
					.getParameter("selectedCurrencyGroup").trim().length() > 0)) ? request
					.getParameter("selectedCurrencyGroup")
					: "All";

			if (!currencyGroup.equalsIgnoreCase("All")) {
				currlist = (ArrayList) SwtUtil.getSwtMaintenanceCache()
						.getCurrencies(
								new EntityCurrencyGroupTO(entityId,
										currencyGroup));
				if ((currlist != null) && (currlist.size() > 0)) {
					Iterator it = currlist.iterator();

					while (it.hasNext()) {
						CurrencyTO currencyTo = (CurrencyTO) it.next();
						currlistLvl
								.add(new LabelValueBean(currencyTo
										.getCurrencyName(), currencyTo
										.getCurrencyId()));
					}
				}

				if (currlistLvl != null) {
					currlistLvl.remove(new LabelValueBean("Default", "*"));
				}

				request.setAttribute("currencydetails", currlistLvl);
			} else {

				MovementSearchManager mgr = (MovementSearchManager) (SwtUtil
						.getBean("movementsearchManager"));

				currlist = getCurrencyList(request, hostId, entityId);

				request.setAttribute("currencydetails", currlist);

			}

			log.debug("Outside currencyList");

			String currencyCode = ((sweepsearch.getCurrencyCode() != null) && (sweepsearch
					.getCurrencyCode().trim().length() > 0)) ? sweepsearch
					.getCurrencyCode().trim() : "%";

			if (currencyCode != null && currencyCode.equals("All")) {
				currencyCode = "%";
			}

			// Populating the account combo box
			Collection accountlist = sweepsearchManager.getAccountDetails(
					hostId, entityId, currencyCode);
			request.setAttribute("accountdetails", accountlist);

			// Populating the generated by combo box
			Collection genlist = sweepsearchManager.getGeneratedDetails(hostId,
					entityId);
			request.setAttribute("gendetails", genlist);

			// Populating the submitted by combo box
			Collection sublist = sweepsearchManager.getSubmittedDetails(hostId,
					entityId);
			request.setAttribute("subdetails", sublist);

			// Populating the authorized by combo box
			Collection authlist = sweepsearchManager.getAuthorizedDetails(
					hostId, entityId);
			request.setAttribute("authdetails", authlist);

			// Populating the message combo box
			Collection msglist = sweepsearchManager.getMessageDetails(hostId,
					entityId);
			request.setAttribute("msgdetails", msglist);

			// Populating the bookcode combo box
			Collection booklist = sweepsearchManager.getBookCodeDetails(hostId,
					entityId);

			request.setAttribute("bookdetails", booklist);

			putCurrencyGroupListInReq(request, hostId, entityId, false);

			// set the preSystemFromDateAsString to request attribute
			if (request.getParameter("preSystemFromDateAsString") != null) {
				request.setAttribute("preSystemFromDateAsString",valueDate);
			}
			// set the preAmountover to request attribute
			if (request.getParameter("preAmountover") != null) {
				request.setAttribute("preAmountover", request
						.getParameter("preAmountover"));
			}
			// set the preAmountunder to request attribute
			if (request.getParameter("preAmountunder") != null) {
				request.setAttribute("preAmountunder", request
						.getParameter("preAmountunder"));
			}
			String parentSrc=request.getParameter("parentScr");
                         if(parentSrc!=null)
			request.setAttribute("parentScreen", parentSrc);
                        String acctType = request.getParameter("acctType");
			if(acctType!=null)
			request.setAttribute("acctType", acctType);
			request.setAttribute("methodName", "search");

			Collection coll = (Collection) request
					.getAttribute("currencydetails");

			log.debug("exiting 'display' method");

			return ("archiveSweep");
		} catch (SwtException swtexp) {
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception e) {
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "display", SweepSearchAction.class), request, "");

			return ("fail");
		}
	}

	/**
	 * @param request
	 * @throws SwtException
	 * 
	 */

	
	public String displaysearch()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();



		try {
			log.debug("entering 'display' method");

			// DynaValidatorForm Sweep sweepsearch = (Sweep) (getSweepsearch());
			Sweep sweepsearch = (Sweep) (getSweepsearch());
			 String parentSrc="";
			String entityId = request.getParameter("entityCode");
			String acctType = request.getParameter("actType");
			if(acctType!=null)
			request.setAttribute("acctType", acctType);
			String currency = request.getParameter("currency");
			String status = request.getParameter("status");

			if (status.equals(SwtConstants.SWEEP_STATUS_NEW)) {
				sweepsearch.setSweepStatus("S");
			}

			if (status.equals(SwtConstants.SWEEP_STATUS_SUBMIT)) {
				sweepsearch.setSweepStatus("U");
			}

			if (status.equals(SwtConstants.SWEEP_STATUS_CANCEL)) {
				sweepsearch.setSweepStatus("B");
			}

			if (entityId.equals("All") || (entityId.trim().length() <= 0)) {
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				sweepsearch.setEntityId(entityId);
			} else {
				sweepsearch.setEntityId(entityId);
				request.setAttribute("disableEntity", "yes");
			}

			if (!currency.equals("All")) {
				sweepsearch.setCurrencyGroup(currency);
				request.setAttribute("disableCcy", "yes");
			}

			if (acctType.equals("All")) {
				sweepsearch.setAccType("A");
			}

			if (acctType.equals("C")) {
				sweepsearch.setAccType("C");
			}

			if (acctType.equals("U")) {
				sweepsearch.setAccType("U");
			}

                        parentSrc=request.getParameter("parentScreen");
                        if(parentSrc!=null)
			request.setAttribute("parentScreen", parentSrc);
			putEntityListInReq(request);

			// Initializing all values to null
			request.setAttribute("currencydetails", new ArrayList());
			request.setAttribute("accountdetails", new ArrayList());
			request.setAttribute("gendetails", new ArrayList());
			request.setAttribute("subdetails", new ArrayList());
			request.setAttribute("authdetails", new ArrayList());
			request.setAttribute("msgdetails", new ArrayList());

			// fetching the host id
			CacheManager cacheManagerInst = CacheManager.getInstance();
			String hostId = cacheManagerInst.getHostId();

			// Populating the currency drop down
			Collection currlist = sweepsearchManager.getCurrencyDetails(hostId,
					entityId);
			request.setAttribute("currencydetails", currlist);
			log.debug("Outside currencyList");

			// Populating the account combo box
			String currencySearch = ((currency != null) && (currency.trim()
					.length() > 0)) ? currency : "%";
			Collection accountlist = sweepsearchManager.getAccountDetails(
					hostId, entityId, currencySearch);
			request.setAttribute("accountdetails", accountlist);

			// Populating the generated by combo box
			Collection genlist = sweepsearchManager.getGeneratedDetails(hostId,
					entityId);
			request.setAttribute("gendetails", genlist);

			// Populating the submitted by combo box
			Collection sublist = sweepsearchManager.getSubmittedDetails(hostId,
					entityId);
			request.setAttribute("subdetails", sublist);

			// Populating the authorized by combo box
			Collection authlist = sweepsearchManager.getAuthorizedDetails(
					hostId, entityId);
			request.setAttribute("authdetails", authlist);

			// Populating the message combo box
			Collection msglist = sweepsearchManager.getMessageDetails(hostId,
					entityId);
			request.setAttribute("msgdetails", msglist);

			// Populating the bookcode combo box
			Collection booklist = sweepsearchManager.getBookCodeDetails(hostId,
					entityId);
			request.setAttribute("bookdetails", booklist);

			putCurrencyGroupListInReq(request, hostId, entityId, false);
			request.setAttribute("methodName", "search");
			log.debug("swwepSearch parameters set ---->" + sweepsearch);
			request.setAttribute("sweepsearch", sweepsearch);
			log.debug("exiting 'display' method");

			return ("success");
		} catch (SwtException swtexp) {
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception e) {
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "displaysearch", SweepSearchAction.class), request, "");

			return ("fail");
		}
	}

	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("entering 'putEntityListInReq' method");

		HttpSession session = request.getSession();
		Collection coll = SwtUtil.getUserEntityAccessList(session);
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entitydetails", coll);

		log.debug("exiting 'putEntityListInReq' method");
	}

	private void putArchiveListinRequest(HttpServletRequest request)
			throws SwtException {
		log.debug("Entering putArchiveListinRequest method");
		MovementSearchManager movementsearchManager = (MovementSearchManager) SwtUtil.getBean("movementsearchManager");
		List archiveList = movementsearchManager.getArchiveList();
		Iterator itr = archiveList.iterator();
		Collection archiveColl = new ArrayList();
		Archive arch = new Archive();

		while (itr.hasNext()) {
			arch = (Archive) itr.next();
			archiveColl.add(new LabelValueBean(arch.getArchiveName(), arch
					.getId().getArchiveId()));
		}

		request.setAttribute("archives", archiveColl);
	}

	/**
	 * This method is used to display the Sweep details. Based on the access get
	 * the details and further displayed on the UI.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String search()
			throws SwtException {
		// variable to hold hostId
		String hostId = null;
		// variable to hold dyForm
		// DynaValidatorForm dyForm = null;

		// variable to hold amountOver
		Double amountOver = null;
		// variable to hold amountUnder
		Double amountUnder = null;
		// variable to hold sweepSearch
		Sweep sweepSearch = null;
		// variable to hold entityId
		String entityId = null;
		// variable to hold status
		String status = null;
		// variable to hold type
		String type = null;
		// variable to hold accType
		String accType = null;
		// variable to hold currencyCode
		String currencyCode = null;
		// variable to hold currencyGroup
		String currencyGroup = null;
		// variable to hold accountId
		String accountId = null;
		// variable to hold amountOverAsString
		String amountOverAsString = null;
		// variable to hold amountunderAsString
		String amountunderAsString = null;
		// variable to hold search list
		Collection<Sweep> searchlist = null;
		// variable to hold currencyCodeArray
		Object[] currencyCodeArray = null;
		// variable to hold itrSearchList
		Iterator<Sweep> itrSearchList = null;
		// variable to hold sweep
		Sweep sweep = null;
		// variable to hold currList
		Collection<LabelValueBean> currencyList = null;
		// variable to hold curryList
		List<LabelValueBean> currList = null;
		String archiveId = null;
		String dbLink = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName() + " - [search] - Entering");
			// Get the host id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// Get the dynavaldator form to dyform
			// get the sweep search details
			sweepSearch = (Sweep) (getSweepsearch());
			// Get the status from request
			status = request.getParameter("status");
			// Get the type from request
			type = request.getParameter("sweeptype");
			// Get the accType from request
			accType = request.getParameter("accounttype");
			// Get the entityId from request
			entityId = request.getParameter("entityId");
			// set the entity id
			if (entityId != null) {
				sweepSearch.setEntityId(entityId);
			}
			// Get the currencyCode from request
			currencyCode = request.getParameter("currencyCode");
			// Get the currencyGroup from request
			currencyGroup = request.getParameter("currencyGroup");
			// Get the currencyGroup from request
			archiveId = request.getParameter("archiveId");
			// Get the accountId from request
			accountId = request.getParameter("accountId");
			// Get the amountOverAsString from request
			amountOverAsString = request.getParameter("amountover");
			// Get the amountunderAsString from request
			amountunderAsString = request.getParameter("amountunder");
			// Get the over and under amount form sweep search
			if (amountOverAsString.equals("")) {
				sweepSearch.setAmountover(new Double(0.0));
				amountOver = sweepSearch.getAmountover();
			} else {
				sweepSearch.setAmountover(SwtUtil.parseCurrency(
						amountOverAsString, SwtUtil.getCurrentSystemFormats(
								request.getSession()).getCurrencyFormat()));
				amountOver = sweepSearch.getAmountover();
			}
			// get the aumount under from sweep search
			if (amountunderAsString.equals("")) {
				sweepSearch.setAmountunder(new Double(0.0));
				amountUnder = sweepSearch.getAmountunder();
			} else {
				sweepSearch.setAmountunder(SwtUtil.parseCurrency(
						amountunderAsString, SwtUtil.getCurrentSystemFormats(
								request.getSession()).getCurrencyFormat()));
				amountUnder = sweepSearch.getAmountunder();
			}

			// Setting the values for account id
			if (SwtUtil.isEmptyOrNull(accountId)) {
				sweepSearch.setAccountId("");
			} else {
				sweepSearch.setAccountId(accountId);
			}
			// Setting the values for currency code
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				sweepSearch.setCurrencyCode("");
			} else {
				sweepSearch.setCurrencyCode(currencyCode);
			}
			// Setting the values for currency group
			if (SwtUtil.isEmptyOrNull(currencyGroup)) {
				sweepSearch.setCurrencyGroup("");
			} else {
				sweepSearch.setCurrencyGroup(currencyGroup);
			}
			// put the currency group list in request
			putCurrencyGroupListInReq(request, hostId, entityId, false);
			/*
			 * Start:code modified by sandeepkumar for Issue Found On STL Testing:1054_STL_002 on 22-mar-12:
			 * system should not allow to display No access currency group
			 * records in the "Sweep search results" screen.
			 * 
			 */
			// get the currency code array detials
			currencyCodeArray = getCurrencyCodeArray(request, hostId, entityId,
					currencyGroup, currencyCode);
			/*
			 * Start:code modified by sandeepkumar for  Issue Found On STL Testing:1054_STL_002 on 22-mar-12:
			 * system should not allow to display No access currency group
			 * records in the "Sweep search results" screen.
			 */
			// Get the search list form manager
			searchlist = sweepsearchManager.fetchdetails(request
					.getParameter("sortorder"), request
					.getParameter("accounttype"), status, type, currencyCode,
					request.getParameter("msgFormat"), accountId, request
							.getParameter("bookCode"), request
							.getParameter("generatedby"), request
							.getParameter("authorisedby"), request
							.getParameter("submittedby"), entityId, hostId,
					request.getParameter("postcutoff"), amountOver,
					amountUnder, request.getParameter("valueFromDateAsString"),
					request.getParameter("valueToDateAsString"), SwtUtil
							.getCurrentSystemFormats(request.getSession()),
					currencyCodeArray, archiveId);

			// iterate the search list
			itrSearchList = searchlist.iterator();
			// instance for sweep
			sweep = new Sweep();
			// To display the amount,user according to the status
			while (itrSearchList.hasNext()) {
				sweep = itrSearchList.next();

				// Process Credit account formats (deep copy matching record)
				if (sweep.getAccountCr().getAccountSweepFormats() != null && !sweep.getAccountCr().getAccountSweepFormats().isEmpty()) {
					for (AccountSpecificSweepFormat record : sweep.getAccountCr().getAccountSweepFormats()) {
						if (record.getId().getSpecifiedAccountId().equals(sweep.getAccountDr().getId().getAccountId())) {

							// Deep copy Credit and Debit accounts
							AcctMaintenance clonedCrAccount = sweep.getAccountCr() != null ? sweep.getAccountCr().deepClone() : null;
							clonedCrAccount.setAcctNewCrInternal(record.getNewInternalCrFormat());
							clonedCrAccount.setAcctNewCrExternal(record.getNewExternalCrFormat());
							sweep.setAccountCr(clonedCrAccount);
							break; // if only one match needed
						}
					}
				}

				// Process Debit account formats (deep copy matching record)
				if (sweep.getAccountDr().getAccountSweepFormats() != null && !sweep.getAccountDr().getAccountSweepFormats().isEmpty()) {
					for (AccountSpecificSweepFormat record : sweep.getAccountDr().getAccountSweepFormats()) {
						if (record.getId().getSpecifiedAccountId().equals(sweep.getAccountCr().getId().getAccountId())) {
							AcctMaintenance clonedDrAccount = sweep.getAccountDr() != null ? sweep.getAccountDr().deepClone() : null;
							clonedDrAccount.setAcctNewDrInternal(record.getNewInternalDrFormat());
							clonedDrAccount.setAcctNewDrExternal(record.getNewExternalDrFormat());
							sweep.setAccountDr(clonedDrAccount);
							break; // if only one match needed
						}
					}
				}



				if (sweep.getOriginalSweepAmt() != null) {
					sweep.setOriginalSweepAmtasstring(SwtUtil.formatCurrency(
							sweep.getCurrencyCode(), sweep
									.getOriginalSweepAmt()));
				}

				if (sweep.getSubmitSweepAmt() != null) {
					sweep
							.setSubmitSweepAmtasstring(SwtUtil.formatCurrency(
									sweep.getCurrencyCode(), sweep
											.getSubmitSweepAmt()));

					sweep.setNewCalulatedAmount(sweep
							.getSubmitSweepAmtasstring());

				}

				if (sweep.getAuthorizeSweepAmt() != null) {
					sweep.setAuthorizeSweepAmtasstring(SwtUtil.formatCurrency(
							sweep.getCurrencyCode(), sweep
									.getAuthorizeSweepAmt()));

					sweep.setNewCalulatedAmount(sweep
							.getAuthorizeSweepAmtasstring());

				}

				if ((status.equals(SwtConstants.SWEEP_STATUS_NEW))
						&& ((type.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
								|| (type.equals(SwtConstants.SWEEP_TYPE_MANUAL)) || (type
								.equals(SwtConstants.SWEEP_TYPE_AUTO)))) {
					sweep.setAmt(sweep.getOriginalSweepAmtasstring());
				} else if ((status.equals(SwtConstants.SWEEP_STATUS_STP))
						&& ((type.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
								|| (type.equals(SwtConstants.SWEEP_TYPE_MANUAL)) || (type
								.equals(SwtConstants.SWEEP_TYPE_AUTO)))) {

					sweep.setAmt(sweep.getOriginalSweepAmtasstring());

				} else if ((status.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
						&& ((type.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
								|| (type.equals(SwtConstants.SWEEP_TYPE_MANUAL)) || (type
								.equals(SwtConstants.SWEEP_TYPE_AUTO)))) {
					sweep.setAmt(sweep.getOriginalSweepAmtasstring());
				} else {
					sweep.setAmt(sweep.getOriginalSweepAmtasstring());
				}

				// Setting Value of Sweep Status
				if (status.equals(SwtConstants.SWEEP_STATUS_NEW)) {
					sweep.setUser(sweep.getInputUser());
				} else if (status.equals(SwtConstants.SWEEP_STATUS_STP)) {
					sweep.setUser(sweep.getSubmitUser());
				} else if (status.equals(SwtConstants.SWEEP_STATUS_SUBMIT)) {
					sweep.setUser(sweep.getAuthorizedUser());
				} else if (status.equals(SwtConstants.BOOK)) {
					sweep.setUser(sweep.getCancelUser());
				} else {
					if ((sweep.getSweepStatus()
							.equals(SwtConstants.SWEEP_STATUS_NEW))
							|| (sweep.getSweepStatus()
									.equals(SwtConstants.SWEEP_STATUS_STP))) {
						sweep.setUser(sweep.getInputUser());
					}

					if (sweep.getSweepStatus().equals(
							SwtConstants.SWEEP_STATUS_SUBMIT)) {
						sweep.setUser(sweep.getSubmitUser());
					}

					if (sweep.getSweepStatus().equals(
							SwtConstants.SWEEP_STATUS_AUTHORIZE)) {
						sweep.setUser(sweep.getAuthorizedUser());
					}

					if (sweep.getSweepStatus().equals(
							SwtConstants.SWEEP_STATUS_CANCEL)) {
						sweep.setUser(sweep.getCancelUser());
					}
				}

				if (sweep.getSweepStatus()
						.equals(SwtConstants.SWEEP_STATUS_NEW)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_NEW);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_SUBMIT)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_SUBMITTED);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_CANCEL)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_CANCELLED);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_STP)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_STP);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_AUTHORIZE)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_AUTHORIZED);
				}

				// Setting Type in list screen
				if (sweep.getSweepType() != null) {
					if (sweep.getSweepType().equals(
							SwtConstants.SWEEP_TYPE_AUTO)) {
						sweep.setSweepType("Auto");
					} else if (sweep.getSweepType().equals(
							SwtConstants.SWEEP_TYPE_MANUAL)) {
						sweep.setSweepType("Manual");
					}
				}

			}
			// put entity list in request
			putEntityListInReq(request);
			// set attribute for search list
			request.setAttribute("searchdisplay", searchlist);
			// checks the account type
			if (accType.equals(SwtConstants.ACCOUNT_TYPE_CASH)) {
				// set the account type to sweep search
				sweepSearch.setAccType("Cash");
			} else if (accType.equals(SwtConstants.ACCOUNT_TYPE_CUSTODIAN)) {
				// set the account type to sweep search
				sweepSearch.setAccType("Custodian");
			} else {
				sweepSearch.setAccType(SwtConstants.ALL_VALUE);
			}
			// checks the currency code is empty or null
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				sweepSearch.setCurrencyCode(SwtConstants.ALL_VALUE);
				// get the currency details from manager
				currList = sweepsearchManager.getCurrencyDetails(hostId,
						entityId);
				// add the label value bean to currlist
				currList.add(0, new LabelValueBean("", SwtConstants.ALL_VALUE));
				// set the currency details in attribute
				request.setAttribute("currencydetails", currList);
			} else {
				// get the currency details form manager
				currencyList = sweepsearchManager.getCurrencyDetails(hostId,
						entityId);
				// set the currency details in attribute
				request.setAttribute("currencydetails", currencyList);
			}
			// set attribute for method name
			request.setAttribute("methodName", "view");
			// set attribute for screen field status
			request.setAttribute("screenFieldsStatus", "true");
			// set attribute for screen field archiveId
			request.setAttribute("archiveId", archiveId);
			
			log.debug(this.getClass().getName() + " - [search] - Exiting");
//			return ("search");
			return ("search");
		} catch (SwtException swtexp) {
			SwtUtil.logException(swtexp, request, "");
			log.error(this.getClass().getName()
					+ " - search(). SwtException : " + swtexp.getMessage());
			return ("fail");
		} catch (Exception e) {
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "search", SweepSearchAction.class), request, "");
			log.error(this.getClass().getName() + " - search(). Exception : "
					+ e.getMessage());
			return ("fail");
		} finally {
			// nullify objects
			hostId = null;
			amountOver = null;
			amountUnder = null;
			sweepSearch = null;
			entityId = null;
			status = null;
			type = null;
			accType = null;
			currencyCode = null;
			currencyGroup = null;
			accountId = null;
			amountOverAsString = null;
			amountunderAsString = null;
			currencyCodeArray = null;
			itrSearchList = null;
			sweep = null;
			currencyList = null;
			currList = null;
		}
	}

	/**
	 * This method is used to get the currency code details
	 * 
	 * @param request
	 * @param hostid
	 * @param entityid
	 * @param currencyGroup
	 * @return
	 */
	/*
	 * Start:code modified by sandeepkumar for  Issue Found On STL Testing:1054_STL_002 on 21-mar-12: system
	 * should not allow to display No access currency group records in the
	 * "Sweep search results" screen.
	 */
	private Object[] getCurrencyCodeArray(HttpServletRequest request,
			String hostid, String entityId, String currencyGroup,
			String currencyCode) throws SwtException {
		// variable to hold currencyCodeList
		Object[] currencyCodeList = null;
		// variable to hold currencyList
		Collection<Object> currencyList = null;
		// variable to hold currLabelList
		Collection<String> currLabelList = null;
		// variable to hold role id
		String roleId = null;
		// variable to hold currency list iterator
		Iterator<Object> currencyListItr = null;
		// variable to hold label value bean
		CurrencyTO currencyTo = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getCurrencyCodeArray] - Entering");
			// Instance for currency list
			currencyList = new ArrayList<Object>();
			// Instance for currency lablel list
			currLabelList = new ArrayList<String>();
			// Getting the role id from common data manager
			roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// checks the currency code is all or empty
			if (currencyCode.equals("All") || currencyCode.trim().equals("")) {
				// checks the currency group is not empty
				if (!currencyGroup.equals("")) {
					// getting the currencies
			currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencies(
									new EntityCurrencyGroupTO(entityId,
											currencyGroup));
		} else {
					// get the currency having access for view and full
			currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
					.getCurrencyViewORFullAcess(roleId, entityId);
		}
			} else {
				// instance for currency list
				currencyList = new ArrayList();
				// add the currency code to currency list
				currencyList.add(new CurrencyTO("", currencyCode, ""));
			}
			// checks the currency list not equal to null
		if (currencyList != null) {
				// adding the default label value bean
			currencyList.remove(new LabelValueBean("Default", "*"));
				// iterate the currency list
				currencyListItr = currencyList.iterator();
				while (currencyListItr.hasNext()) {
					currencyTo = (CurrencyTO) currencyListItr.next();
					currLabelList.add(currencyTo.getCurrencyId());
			}
				currencyCodeList = currLabelList.toArray();
		} else {
			currencyCodeList = null;
		}
			log.debug(this.getClass().getName()
					+ " - [getCurrencyCodeArray] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - [getCurrencyCodeArray] - Exception -"
					+ e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currencyList = null;
			currLabelList = null;
			roleId = null;
			currencyListItr = null;
			currencyTo = null;
		}
		return currencyCodeList;
	}

	/*
	 * End:code modified by sandeepkumar for  Issue Found On STL Testing:1054_STL_002 on 21-mar-12: system should
	 * not allow to display No access currency group records in the "Sweep
	 * search results" screen.
	 */
	/**
	 * This method is used to display view associated sweepSearch,account and
	 * entity details into the request that is further displayed on the UI.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String view()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable to hold accountIdCr
		String accountIdCr = null;
		// variable to hold accountIdDr
		String accountIdDr = null;
		// variable to hold entityIdCr
		String entityIdCr = null;
		// variable to hold entityIddr
		String entityIddr = null;
		// variable to hold roleId
		String roleId = null;
		// variable to hold dyForm
		// DynaValidatorForm dyForm = null;
		// variable to hold sweepsearch
		Sweep sweepSearch = null;
		// variable to hold sweepvalue
		Long sweepValue = null;
		// variable to hold movementiddr
		Long movementIdDr = null;
		// variable to hold movementidcr
		Long movementIdCr = null;
		// variable to hold hostid
		String hostId = null;
		// variable to hold entityid
//		String entityId = null;
		// variable to hold itrSweep
		Iterator<Sweep> itrSweep = null;
		// variable to hold sweep
		Sweep sweep = null;
		// variable to hold simpleDateFormat
		SimpleDateFormat simpleDateFormat = null;
		// variable to hold sweepCutOff
		Sweep sweepCutOff = null;
		// variable to hold posItr
		Iterator<LabelValueBean> posItr = null;
		// variable to hold drItr
		Iterator<Movement> drItr = null;
		// variable to hold itrMiscParams
		Iterator<MiscParams> itrMiscParams = null;
		// variable to hold movDetails
		Collection<Movement> movDetails = null;
		// variable to hold movDr
		Movement movDr = null;
		// variable to hold movCr
		Movement movCr = null;
		// variable to hold itrMiscParamsExternal
		Iterator<MiscParams> itrMiscParamsExternal = null;
		// variable to hold crItr
		Iterator<Movement> crItr = null;
		// variable to hold collMiscParamsExt
		Collection<MiscParams> collMiscParamsExt = null;
		// variable to hold collMiscParams
		Collection<MiscParams> collMiscParams = null;
		// variable to hold sweep details
		Collection<Sweep> sweepDetails = null;
		// variable to hold movDetailsCr
		Collection<Movement> movDetailsCr = null;
		// variable to hold position level names
		Collection collPositionLvlNames = null;
		// Variable for MiscParams
		MiscParams mParams = null;
		
		String archiveId = null;
		
		// To get Entity Access list
		Collection<EntityUserAccess> entityUserAccessList = null;
		// To iterate entity access list collection
		Iterator<EntityUserAccess> entityUserAccessItr = null;
		// To get EntityUserAccess
		EntityUserAccess entityUserAccess = null;

		try {
			log.debug("Inside the view method");
			// get the form assign into the dynavalidator form
			// get the form sweep search from form
			sweepSearch = (Sweep) (getSweepsearch());
			// set attribute for screenFieldsStatus
			request.setAttribute("screenFieldsStatus", "true");
			// set attribute for archiveId
			request.setAttribute("archiveId" , request.getParameter("archiveId"));
			// set attribute for methodName
			request.setAttribute("methodName", "view");
			// set attribute for sweepamount
			request.setAttribute("sweepamount", request
					.getParameter("submitamt"));
			// Get the sweep value
			sweepValue = new Long(Long.parseLong(request
					.getParameter("sweepId").trim()));
			
			archiveId  =  request.getParameter("archiveId");
			sweepSearch.getId().setSweepId(sweepValue);
			// Get the host id form cache manager
			hostId = CacheManager.getInstance().getHostId();
			// get the selected sweep id
			if (request.getParameter("selectedSweepId") != null) {
				sweepSearch.getId().setSweepId(
						new Long(request.getParameter("selectedSweepId")));
			}
			
			// Get the role id form common date manager
			roleId = ((CommonDataManager) request.getSession()
					.getAttribute(SwtConstants.CDM_BEAN)).getUser()
					.getRoleId();
			
			// Instance for debit movement id
			movementIdDr = new Long(0);
			// Instance for credit movement id
			movementIdCr = new Long(0);

			if ((sweepSearch.getId().getSweepId() != null)) {
				// fetch the sweep details
				if(archiveId != null) {
					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepValue, hostId, archiveId);
				}else
					sweepDetails = sweepsearchManager.fetchsweepdetails(sweepValue,	hostId);
				// iterate the sweep details
				itrSweep = sweepDetails.iterator();
				// Instance for sweep
				sweep = new Sweep();
				// instance for simple date format
				simpleDateFormat = new SimpleDateFormat(SwtUtil
						.getCurrentDateFormat(request.getSession())
						+ " HH:mm:ss");
				while (itrSweep.hasNext()) {
					// iterate the sweep values
					sweep = itrSweep.next();

					if (sweep.getInputDateTime() != null) {
						sweep.setInputtime(simpleDateFormat.format(sweep
								.getInputDateTime()));
					}
					// Get the entity id
//					entityId = sweep.getId().getEntityId();
					// get the credit entity id
					entityIdCr = sweep.getEntityIdCr();
					// get the debit entity id
					entityIddr = sweep.getEntityIdDr();
					// get the authorise date time and checks not equal to null
					if (sweep.getAuthDateTime() != null) {
						sweep.setAuthorisetime(simpleDateFormat.format(sweep
								.getAuthDateTime()));
					}

					if (sweep.getSubmitDateTime() != null) {
						sweep.setSubmittedtime(simpleDateFormat.format(sweep
								.getSubmitDateTime()));
					}

					if ((sweep.getMovementIdDr() != null)
							|| (sweep.getMovementIdCr() != null)) {

						movementIdDr = new Long(Long.parseLong(sweep
								.getMovementIdDr().trim()));
						movementIdCr = new Long(Long.parseLong(sweep
								.getMovementIdCr().trim()));
					}
					// get the credit account id
					accountIdCr = sweep.getAccountIdCr();
					// get the debit account id
					accountIdDr = sweep.getAccountIdDr();

					// Get the original sweep amount and checks not equal to
					// null
					if (sweep.getOriginalSweepAmt() != null) {
						sweep.setOriginalSweepAmtasstring(SwtUtil
								.formatCurrency(sweep.getCurrencyCode(), sweep
										.getOriginalSweepAmt()));
					}

					if (sweep.getSubmitSweepAmt() != null) {
						sweep.setSubmitSweepAmtasstring(SwtUtil.formatCurrency(
								sweep.getCurrencyCode(), sweep
										.getSubmitSweepAmt()));
					}

					if (sweep.getAuthorizeSweepAmt() != null) {
						sweep.setAuthorizeSweepAmtasstring(SwtUtil
								.formatCurrency(sweep.getCurrencyCode(), sweep
										.getAuthorizeSweepAmt()));
					}
					// Get the original sweep status and checks not equal to
					// null
					if (sweep.getSweepStatus() != null) {
						if (sweep.getSweepStatus().equals(
								SwtConstants.SWEEP_STATUS_NEW)) {
							sweep.setSweepStatus(SwtConstants.SWEEP_NEW);
							sweep.setAmt(sweep.getOriginalSweepAmtasstring());
						} else if (sweep.getSweepStatus().equals(
								SwtConstants.SWEEP_STATUS_STP)) {
							sweep.setSweepStatus(SwtConstants.SWEEP_STP);
							sweep.setAmt(sweep.getOriginalSweepAmtasstring());
						} else if (sweep.getSweepStatus().equals(
								SwtConstants.SWEEP_STATUS_SUBMIT)) {
							sweep.setSweepStatus(SwtConstants.SWEEP_SUBMITTED);
							sweep.setAmt(sweep.getSubmitSweepAmtasstring());
						} else if (sweep.getSweepStatus().equals(
								SwtConstants.SWEEP_STATUS_CANCEL)) {
							sweep.setSweepStatus(SwtConstants.SWEEP_CANCELLED);
							sweep.setAmt(sweep.getOriginalSweepAmtasstring());
						} else if (sweep.getSweepStatus().equals(
								SwtConstants.SWEEP_STATUS_AUTHORIZE)) {
							sweep.setSweepStatus(SwtConstants.SWEEP_AUTHORIZED);
							sweep.setAmt(sweep.getAuthorizeSweepAmtasstring());
						}
					}
					// Get the sweep type and checks not equal to null
					if (sweep.getSweepType() != null) {
						if (sweep.getSweepType().equals(
								SwtConstants.SWEEP_TYPE_AUTO)) {
							sweep.setSweepType("Auto");
						} else if (sweep.getSweepType().equals(
								SwtConstants.SWEEP_TYPE_MANUAL)) {
							sweep.setSweepType("Manual");
						}
					}
					// Get the value date and checks not equal to null
					if (sweep.getValueDate() != null) {
						sweep.setValueDateAsString(SwtUtil.formatDate(sweep
								.getValueDate(), SwtUtil
								.getCurrentSystemFormats(request.getSession())
								.getDateFormatValue()));
					}
				}
				ArchiveManager archiveManager = (ArchiveManager) SwtUtil
						.getBean("archiveManager");
				// Get the sweep cut off from the sweep search manager
				sweepCutOff = sweepsearchManager.fetchsweepCutOff(accountIdCr,
						accountIdDr, entityIdCr, entityIddr, hostId,
						sweepSearch.getId().getSweepId(), sweep, archiveId);
				// Get the submitted cut off flag and checks not equal to null
				if (sweepCutOff.getSubmittedpostcutoffflg() != null) {
					if (sweepCutOff.getSubmittedpostcutoffflg()
							.equalsIgnoreCase(SwtConstants.YES)) {
						sweep.setSubmittedpostcutoffflg(SwtConstants.YES);
					} else {
						sweep.setSubmittedpostcutoffflg(SwtConstants.NO);
					}
				}

				if (sweepCutOff.getGeneratedpostcutoffflg() != null) {
					if (sweepCutOff.getGeneratedpostcutoffflg()
							.equalsIgnoreCase(SwtConstants.YES)) {
						sweep.setGeneratedpostcutoffflg(SwtConstants.YES);
					} else {
						sweep.setGeneratedpostcutoffflg(SwtConstants.NO);
					}
				}

				if (sweepCutOff.getAuthorisedpostcutoffflg() != null) {
					if (sweepCutOff.getAuthorisedpostcutoffflg()
							.equalsIgnoreCase(SwtConstants.YES)) {
						sweep.setAuthorisedpostcutoffflg(SwtConstants.YES);
					} else {
						sweep.setAuthorisedpostcutoffflg(SwtConstants.NO);
					}
				}
				
				entityUserAccessList = SwtUtil.getSwtMaintenanceCache()
						.getEntityAccessCollection(new RoleTO(roleId));
				
				
				entityUserAccessItr = entityUserAccessList
						.iterator();
				String entityToPass = null;
				while (entityUserAccessItr.hasNext()) {
					entityUserAccess = entityUserAccessItr.next();
					// Checks current entity id match with user
					// accessible entity list and access
					if (entityUserAccess.getEntityId().equals(
							entityIdCr) || entityUserAccess.getEntityId().equals(
									entityIddr)) {
						entityToPass = entityUserAccess.getEntityId();
					} 
				}
				
				
				// set the attribute for entityid
				request.setAttribute("entityId", entityToPass);
				
				// set attribute for entity id
				request.setAttribute("entityId", null);

				// To set the values in dynavalidator form
				setSweepsearch(sweep);

			
				int accessCr = SwtUtil
						.getSwtMaintenanceCache().getCurrencyAccess(roleId,
								sweep.getEntityIdCr(),
								sweep.getCurrencyCode());
				int accessDr = SwtUtil
						.getSwtMaintenanceCache().getCurrencyAccess(roleId,
								sweep.getEntityIdDr(),
								sweep.getCurrencyCode());
				
				int access = Math.max(accessCr, accessDr);
				request.setAttribute("currGrpAccess", access);

				// fetching the movement table values
				if ((sweep.getMovementIdDr() != null)
						|| (sweep.getMovementIdCr() != null)) {
					sweep.setOutMessageDr(SwtConstants.SweepOutGoingMessage);
					sweep.setOutMessageCr(SwtConstants.SweepOutGoingMessage);
					if(SwtUtil.isEmptyOrNull(archiveId)) {
						movDetails = sweepsearchManager.fetchMovementDetails(
								hostId, entityIddr, movementIdDr);
					}else {
						 MovementManager movManger = (MovementManager) SwtUtil.getBean("movementManager");
						 Movement movementDr = movManger.getArchiveMovementDetails(entityIddr, movementIdDr, archiveId);
						 
						 if(movementDr != null) {
							 movDetails = new ArrayList<>();
							 movDetails.add(movementDr);
						 }
						
					}
					// Get the movement details from sweep search manager
					// Iterate the movement details
					drItr = movDetails.iterator();
					// Instance for movement
					movDr = new Movement();

					while (drItr.hasNext()) {
						movDr = drItr.next();

						if (movDr.getBookCode() != null) {
							// Set attribute for book code debit
							request.setAttribute("bookcodedr", movDr
									.getBookCode());
						} else {
							// Set attribute for book code credit
							request.setAttribute("bookcodedr", "");
						}

						if (movDr.getMatchId() != null) {
							// Set attribute for match id debit
							request.setAttribute("matchiddr", movDr
									.getMatchId().toString());
						} else {
							request.setAttribute("matchiddr", "");
						}

						if (movDr.getPositionLevel() != null) {
							// get the position level names
							collPositionLvlNames = SwtUtil
									.getSwtMaintenanceCache()
									.getEntityPositionLevelObjectLVL(entityIddr);
							// iterating the position level iterator
							posItr = collPositionLvlNames.iterator();

							while (posItr.hasNext()) {
								// get the label value bean
								LabelValueBean lblValueBean = posItr.next();

								if (movDr.getPositionLevel().toString().equals(
										lblValueBean.getValue())) {
									// set attribute for position level debit
									request.setAttribute("positionleveldr",
											lblValueBean.getLabel());
								}
							}

						} else {
							request.setAttribute("positionleveldr", "");
						}
						// get the coll miscparmas form cache manager
						collMiscParams = (Collection) CacheManager
								.getInstance().getMiscParams("PREDICTSTATUS",
										entityIddr);
						// Iterate the itrmiscparm values
						itrMiscParams = collMiscParams.iterator();

						while (itrMiscParams.hasNext()) {
							mParams = (itrMiscParams.next());
							if ((movDr.getPredictStatus() != null)
									|| (!"".equals(movDr.getPredictStatus()))) {
								if (movDr.getPredictStatus().equals(
										mParams.getId().getKey2())) {
									// set attribute for predict status
									request.setAttribute("predictstatusdr",
											mParams.getParValue());
									break;
								} else {
									request.setAttribute("predictstatusdr", "");
								}
							}
						}
						// Get the collmisc params external form cache manager
						collMiscParamsExt = (Collection) CacheManager
								.getInstance().getMiscParams("PREDICTSTATUS",
										entityIddr);
						// Iterate the itrMiscParamsExternal
						itrMiscParamsExternal = collMiscParamsExt.iterator();

						while (itrMiscParamsExternal.hasNext()) {
							mParams = (itrMiscParamsExternal.next());

							if ((movDr.getExtBalStatus() != null)
									|| (!"".equals(movDr.getExtBalStatus()))) {
								if (movDr.getExtBalStatus().equals(
										mParams.getId().getKey2())) {
									// set attribute for externalstatusdr
									request.setAttribute("externalstatusdr",
											mParams.getParValue());
									break;
								} else {
									request
											.setAttribute("externalstatusdr",
													"");
								}
							}
						}

					}

					// Fetch the movement details
					if(SwtUtil.isEmptyOrNull(archiveId)) {
						movDetailsCr = sweepsearchManager.fetchMovementDetails(
								hostId, entityIdCr, movementIdCr);
					}else {
						 MovementManager movManger = (MovementManager) SwtUtil.getBean("movementManager");
						 Movement movementCr = movManger.getArchiveMovementDetails(entityIdCr, movementIdCr, archiveId);
						 
						 if(movementCr != null) {
							 movDetailsCr = new ArrayList<>();
							 movDetailsCr.add(movementCr);
						 }
						
					}
					// iterate the crItr
					crItr = movDetailsCr.iterator();
					// Instance for movement
					movCr = new Movement();

					while (crItr.hasNext()) {
						movCr = crItr.next();

						if (movCr.getBookCode() != null) {
							// set attribute for bookcodecr
							request.setAttribute("bookcodecr", movCr
									.getBookCode());
						} else {
							request.setAttribute("bookcodecr", "");
						}

						if (movCr.getMatchId() != null) {
							// set attribute for matchidcr
							request.setAttribute("matchidcr", movCr
									.getMatchId().toString());
						} else {
							request.setAttribute("matchidcr", "");
						}
						// Get the position level and check not equal to null
						if (movCr.getPositionLevel() != null) {
							collPositionLvlNames = SwtUtil
									.getSwtMaintenanceCache()
									.getEntityPositionLevelObjectLVL(entityIdCr);
							// Iterate the posItr
							posItr = collPositionLvlNames.iterator();

							while (posItr.hasNext()) {
								LabelValueBean lvl = (LabelValueBean) posItr
										.next();

								if (movCr.getPositionLevel().toString().equals(
										lvl.getValue())) {
									request.setAttribute("positionlevelcr", lvl
											.getLabel());
								}
							}

						} else {
							// set attribute for position level credit
							request.setAttribute("positionlevelcr", "");
						}
						collMiscParams = (Collection) CacheManager
								.getInstance().getMiscParams("PREDICTSTATUS",
										entityIdCr);
						itrMiscParams = collMiscParams.iterator();

						while (itrMiscParams.hasNext()) {
							// Get the mparams from itrmiscparams
							mParams = (MiscParams) (itrMiscParams.next());
							if ((movCr.getPredictStatus() != null)
									|| (!"".equals(movCr.getPredictStatus()))) {
								if (movCr.getPredictStatus().equals(
										mParams.getId().getKey2())) {
									request.setAttribute("predictstatuscr",
											mParams.getParValue());
									break;
								} else {
									// set attribute for predictstatuscr
									request.setAttribute("predictstatuscr", "");
								}
							}
						}
						// Get the collMiscParams external values
						collMiscParamsExt = (Collection) CacheManager
								.getInstance().getMiscParams("PREDICTSTATUS",
										entityIdCr);
						// Iterate itrMiscParamsExternal
						itrMiscParamsExternal = collMiscParamsExt.iterator();

						while (itrMiscParamsExternal.hasNext()) {
							mParams = (MiscParams) (itrMiscParamsExternal
									.next());
							if ((movCr.getExtBalStatus() != null)
									|| (!"".equals(movCr.getExtBalStatus()))) {
								if (movCr.getExtBalStatus().equals(
										mParams.getId().getKey2())) {
									request.setAttribute("externalstatuscr",
											mParams.getParValue());
									break;
								} else {
									// set attribute for externalstatuscr
									request
											.setAttribute("externalstatuscr",
													"");
								}
							}
						}

					}
				}
			} else {

				throw new SwtException("sweepId.doesnotmatch", "N");
			}
			log.debug("exiting 'view' method");
			return ("view");

		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log.error(this.getClass().getName() + " - view(). Exception : "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return ("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "view", SweepSearchAction.class), request, "");
			log.error(this.getClass().getName() + " - view(). Exception : "
					+ e.getMessage());
			return ("fail");
		} finally {
			accountIdCr = null;
			accountIdDr = null;
			entityIdCr = null;
			entityIddr = null;
			roleId = null;
			sweepSearch = null;
			sweepValue = null;
			movementIdDr = null;
			movementIdCr = null;
			hostId = null;
//			entityId = null;
			itrSweep = null;
			sweep = null;
			simpleDateFormat = null;
			sweepCutOff = null;
			posItr = null;
			drItr = null;
			itrMiscParams = null;
			movDetails = null;
			movDr = null;
			movCr = null;
			itrMiscParamsExternal = null;
			crItr = null;
			collMiscParamsExt = null;
			collMiscParams = null;
			movDetailsCr = null;
			mParams = null;
		}
	}

	/**
	 * This method is used to populate associated sweepSearch,account and entity
	 * details into the request that is further displayed on the UI.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String populate()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		// variable to hold accountIdCr
		String accountIdCr = null;
		// variable to hold accountIdDr
		String accountIdDr = null;
		// variable to hold entityIdCr
		String entityIdCr = null;
		// variable to hold entityIddr
		String entityIdDr = null;
		// variable to hold entity id
//		String entityId = null;
		// initializing sweep
		Sweep sweep = null;
		// variable to hold dyForm
		// DynaValidatorForm dyForm = null;
		// variable to hold sweepSearch
		Sweep sweepSearch = null;
		// variable to hold hostId
		String hostId = null;
		// variable to hold sweepCutOff
		Sweep sweepCutOff = null;
		// Variable for MiscParam
		MiscParams mParams = null;
		// variable to hold movDetails
		Collection<Movement> movDetails = null;
		// variable to hold movement debit
		Movement movDr = null;
		// variable to hold collMiscParams
		Collection<MiscParams> collMiscParams = null;
		// variable to hold collMiscParams external
		Collection<MiscParams> collMiscParamsExternal = null;
		// variable to hold movDetailsCr
		Collection<Movement> movDetailsCr = null;
		// variable to hold sweepdetails
		Collection<Sweep> sweepDetails = null;
		// variable to hold movement id debit
		Long movementIdDr = null;
		// variable to hold movement id credit
		Long movementIdCr = null;
		// variable to hold movement credit
		Movement mvmtCr = null;
		// variable to hold role id
		String roleId = null;
		// variable to hold simple date format
		SimpleDateFormat simpleDateFormat = null;
		// Iterating the debitItr
		Iterator<Movement> debitItr = null;
		// Iterating the itrMiscParamsext
		Iterator<MiscParams> itrMiscParamsExt = null;
		// Iterating the creditItr
		Iterator<Movement> creditItr = null;
		// iterating the miscparams
		Iterator<MiscParams> itrMiscParams = null;
		// iterating the position
		Iterator<LabelValueBean> positionItr = null;
		// To get Entity Access list
		Collection<EntityUserAccess> entityUserAccessList = null;
		// To iterate entity access list collection
		Iterator<EntityUserAccess> entityUserAccessItr = null;
		// To get EntityUserAccess
		EntityUserAccess entityUserAccess = null;
		// To Get Entity Access
		String currEntityAccess = null;
		// variable to hold sweep id
		Long sweepId = null;
		// variable to hold the sweepDetailsItr object
		Iterator<Sweep> sweepDetailsItr = null;
		
		String dbLink = null;
		String archiveId = null;
		try {
			log.debug(this.getClass().getName() + "- [populate] - Entering ");
			// get the form assign into the dynavalidator form
			// get the Sweep assign into the Sweepsearch form
			sweepSearch = (Sweep) (getSweepsearch());
			// set the attribute for screenFieldsStatus
			request.setAttribute("screenFieldsStatus", "true");
			// Get the hostid from cache manager
			hostId = CacheManager.getInstance().getHostId();
			/* Start:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			// Check selected selected sweep id is null or empty and set in the
			// bean
			if (!SwtUtil.isEmptyOrNull(request.getParameter("selectedSweepId"))) {
				sweepSearch.getId().setSweepId(
						new Long(request.getParameter("selectedSweepId")));
			} else {
				sweepSearch.getId().setSweepId(null);
			}
			/* End:Code modified by sudhakar For Mantis 1561_1054_STL_003 */
			movementIdDr = new Long(0);
			movementIdCr = new Long(0);
			if ((sweepSearch.getId().getSweepId() != null)) {
				sweepId = sweepSearch.getId().getSweepId();
				// Get the sweep details collection form manager
				sweepDetails = sweepsearchManager.fetchsweepdetails(sweepId,
						hostId);
				
//				sweepDetails = new ArrayList<Sweep>();
				if (sweepDetails.size() == 0) {

					ArchiveManager archiveManager = (ArchiveManager) (SwtUtil
							.getBean("archiveManager"));
					Archive arch = archiveManager.getCurrentArchiveDb(hostId);

					if (arch != null) {
						archiveId = arch.getId().getArchiveId();
						dbLink = arch.getDb_link();
					}

					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepId,
							hostId, archiveId);
					if (sweepDetails.size() > 0) {
						request.setAttribute("archiveId", archiveId);
					}
					
				}else {
					request.setAttribute("archiveId", "");
				}
				
				
				if (sweepDetails.size() > 0) {
					sweepDetailsItr = sweepDetails.iterator();
					// instance for sweep
					sweep = new Sweep();
					simpleDateFormat = new SimpleDateFormat(SwtUtil
							.getCurrentDateFormat(request.getSession())
							+ " HH:mm:ss");
					while (sweepDetailsItr.hasNext()) {
						sweep = sweepDetailsItr.next();

						if (sweep.getInputDateTime() != null) {
							sweep.setInputtime(simpleDateFormat.format(sweep
									.getInputDateTime()));
						}
						// Get the role id form common date manager
						roleId = ((CommonDataManager) request.getSession()
								.getAttribute(SwtConstants.CDM_BEAN)).getUser()
								.getRoleId();
						// get the entity id form sweep
//						entityId = sweep.getId().getEntityId();
						
						
						// get the EntityIdCr
						entityIdCr = sweep.getEntityIdCr();
						// Get the EntityIdDr
						entityIdDr = sweep.getEntityIdDr();
						
						// Get the role entity user access list
						entityUserAccessList = SwtUtil.getSwtMaintenanceCache()
								.getEntityAccessCollection(new RoleTO(roleId));
						// Set default value as no
						currEntityAccess = SwtConstants.NO;
						// Checks entity user access list
						if (sweep != null && entityUserAccessList != null) {
							// Gets the iterator
							entityUserAccessItr = entityUserAccessList
									.iterator();
							while (entityUserAccessItr.hasNext()) {
								entityUserAccess = entityUserAccessItr.next();
								// Checks current entity id match with user
								// accessible entity list and access
								if (entityUserAccess.getEntityId().equals(
										entityIdCr) || entityUserAccess.getEntityId().equals(
												entityIdDr)) {
									currEntityAccess = SwtConstants.YES;
									break;
								} else {
									currEntityAccess = SwtConstants.NO;
								}
							}
							// Check currency access
							if (currEntityAccess == SwtConstants.YES
									&& (SwtUtil
											.getSwtMaintenanceCache()
											.getCurrencyAccess(
													roleId,
													entityIdCr,
													sweep.getCurrencyCode()) == SwtConstants.CURRENCYGRP_NO_ACCESS || SwtUtil
													.getSwtMaintenanceCache()
													.getCurrencyAccess(
															roleId,
															entityIdDr,
															sweep.getCurrencyCode()) == SwtConstants.CURRENCYGRP_NO_ACCESS)) {
								currEntityAccess = SwtConstants.NO;
							}
						}
						if (currEntityAccess.equals(SwtConstants.NO)) {
							// set the attribute for ifNosweepDetailsPresent
							request.setAttribute("sweepDoesNotHaveAccess",
									"true");
							throw new SwtException("sweepId.alert.noAccess",
									"N");
						}
						if (sweep.getAuthDateTime() != null) {
							sweep.setAuthorisetime(simpleDateFormat
									.format(sweep.getAuthDateTime()));
						}

						if (sweep.getSubmitDateTime() != null) {
							sweep.setSubmittedtime(simpleDateFormat
									.format(sweep.getSubmitDateTime()));
						}

						if ((sweep.getMovementIdDr() != null)
								|| (sweep.getMovementIdCr() != null)) {
							movementIdDr = new Long(Long.parseLong(sweep
									.getMovementIdDr().trim()));
							movementIdCr = new Long(Long.parseLong(sweep
									.getMovementIdCr().trim()));
						}
						// get the AccountIdCr
						accountIdCr = sweep.getAccountIdCr();
						// Get the AccountIdDr
						accountIdDr = sweep.getAccountIdDr();


						if (sweep.getOriginalSweepAmt() != null) {
							sweep.setOriginalSweepAmtasstring(SwtUtil
									.formatCurrency(sweep.getCurrencyCode(),
											sweep.getOriginalSweepAmt()));
						}

						if (sweep.getSubmitSweepAmt() != null) {
							sweep.setSubmitSweepAmtasstring(SwtUtil
									.formatCurrency(sweep.getCurrencyCode(),
											sweep.getSubmitSweepAmt()));
						}

						if (sweep.getAuthorizeSweepAmt() != null) {
							sweep.setAuthorizeSweepAmtasstring(SwtUtil
									.formatCurrency(sweep.getCurrencyCode(),
											sweep.getAuthorizeSweepAmt()));
						}

						if (sweep.getSweepStatus() != null) {
							if (sweep.getSweepStatus().equals(
									SwtConstants.SWEEP_STATUS_NEW)) {
								sweep.setSweepStatus(SwtConstants.SWEEP_NEW);
								sweep.setAmt(sweep
										.getOriginalSweepAmtasstring());
							} else if (sweep.getSweepStatus().equals(
									SwtConstants.SWEEP_STATUS_STP)) {
								sweep.setSweepStatus(SwtConstants.SWEEP_STP);
								sweep.setAmt(sweep
										.getOriginalSweepAmtasstring());
							} else if (sweep.getSweepStatus().equals(
									SwtConstants.SWEEP_STATUS_SUBMIT)) {
								sweep
										.setSweepStatus(SwtConstants.SWEEP_SUBMITTED);
								sweep.setAmt(sweep.getSubmitSweepAmtasstring());
							} else if (sweep.getSweepStatus().equals(
									SwtConstants.SWEEP_STATUS_CANCEL)) {
								sweep
										.setSweepStatus(SwtConstants.SWEEP_CANCELLED);
								sweep.setAmt(sweep
										.getOriginalSweepAmtasstring());
							} else if (sweep.getSweepStatus().equals(
									SwtConstants.SWEEP_STATUS_AUTHORIZE)) {
								sweep
										.setSweepStatus(SwtConstants.SWEEP_AUTHORIZED);
								sweep.setAmt(sweep
										.getAuthorizeSweepAmtasstring());
							}
						}

						if (sweep.getSweepType() != null) {
							if (sweep.getSweepType().equals(
									SwtConstants.SWEEP_TYPE_AUTO)) {
								sweep.setSweepType(SwtConstants.SWEEP_AUTO);
							} else if (sweep.getSweepType().equals(
									SwtConstants.SWEEP_TYPE_MANUAL)) {
								sweep.setSweepType(SwtConstants.SWEEP_MANUAL);
							}
						}

						if (sweep.getValueDate() != null) {
							sweep.setValueDateAsString(SwtUtil.formatDate(sweep
									.getValueDate(), SwtUtil
									.getCurrentSystemFormats(
											request.getSession())
									.getDateFormatValue()));
						}
					}
					if(SwtUtil.isEmptyOrNull(dbLink)) {
						sweepCutOff = sweepsearchManager.fetchsweepCutOff(
								accountIdCr, accountIdDr, entityIdCr, entityIdDr,
								hostId, sweepSearch.getId().getSweepId(), sweep, "");
					}else {
						sweepCutOff = sweepsearchManager.fetchsweepCutOff(
								accountIdCr, accountIdDr, entityIdCr, entityIdDr,
								hostId, sweepSearch.getId().getSweepId(), sweep, dbLink);
					}

					if (sweepCutOff.getSubmittedpostcutoffflg() != null) {
						if (sweepCutOff.getSubmittedpostcutoffflg()
								.equalsIgnoreCase("Y")) {
							sweep.setSubmittedpostcutoffflg("Y");
						} else {
							sweep.setSubmittedpostcutoffflg("N");
						}
					}

					if (sweepCutOff.getGeneratedpostcutoffflg() != null) {
						if (sweepCutOff.getGeneratedpostcutoffflg()
								.equalsIgnoreCase("Y")) {
							sweep.setGeneratedpostcutoffflg("Y");
						} else {
							sweep.setGeneratedpostcutoffflg("N");
						}
					}

					if (sweepCutOff.getAuthorisedpostcutoffflg() != null) {
						if (sweepCutOff.getAuthorisedpostcutoffflg()
								.equalsIgnoreCase("Y")) {
							sweep.setAuthorisedpostcutoffflg("Y");
						} else {
							sweep.setAuthorisedpostcutoffflg("N");
						}
					}
					entityUserAccessItr = entityUserAccessList
							.iterator();
					String entityToPass = null;
					while (entityUserAccessItr.hasNext()) {
						entityUserAccess = entityUserAccessItr.next();
						// Checks current entity id match with user
						// accessible entity list and access
						if (entityUserAccess.getEntityId().equals(
								entityIdCr) || entityUserAccess.getEntityId().equals(
										entityIdDr)) {
							entityToPass = entityUserAccess.getEntityId();
						} 
					}
					
					
					// set the attribute for entityid
					request.setAttribute("entityId", entityToPass);

					// To set the values in dynavalidator form
					setSweepsearch(sweep);
					
					int accessCr = SwtUtil
							.getSwtMaintenanceCache().getCurrencyAccess(roleId,
									sweep.getEntityIdCr(),
									sweep.getCurrencyCode());
					int accessDr = SwtUtil
							.getSwtMaintenanceCache().getCurrencyAccess(roleId,
									sweep.getEntityIdDr(),
									sweep.getCurrencyCode());
					
					int access = Math.max(accessCr, accessDr);
					
					request.setAttribute("currGrpAccess", access);

					// fetching the movement table values
					if ((sweep.getMovementIdDr() != null)
							|| (sweep.getMovementIdCr() != null)) {
						sweep
								.setOutMessageDr(SwtConstants.SweepOutGoingMessage);
						sweep
								.setOutMessageCr(SwtConstants.SweepOutGoingMessage);

						// For Debit account

						
						if(SwtUtil.isEmptyOrNull(dbLink)) {
							movDetails = sweepsearchManager.fetchMovementDetails(
									hostId, entityIdDr, movementIdDr);
						}else {
							 MovementManager movManger = (MovementManager) SwtUtil.getBean("movementManager");
							 Movement movementDr = movManger.getArchiveMovementDetails(entityIdDr, movementIdDr, archiveId);
							 
							 if(movementDr != null) {
								 movDetails = new ArrayList<>();
								 movDetails.add(movementDr);
							 }
							
						}
						
						
						debitItr = movDetails.iterator();
						// Instance of movement
						movDr = new Movement();

						while (debitItr.hasNext()) {
							movDr = debitItr.next();

							if (movDr.getBookCode() != null) {
								// set the attribute for book code
								request.setAttribute("bookcodedr", movDr
										.getBookCode());
							} else {
								// set the attribute for bookcodedr
								request.setAttribute("bookcodedr", "");
							}

							if (movDr.getMatchId() != null) {
								request.setAttribute("matchiddr", movDr
										.getMatchId().toString());
							} else {
								// set attribute for match id
								request.setAttribute("matchiddr", "");
							}

							if (movDr.getPositionLevel() != null) {
								Collection collPositionLvlNames = SwtUtil
										.getSwtMaintenanceCache()
										.getEntityPositionLevelObjectLVL(
												entityIdDr);
								// iterate position level
								positionItr = collPositionLvlNames.iterator();

								while (positionItr.hasNext()) {
									LabelValueBean lvl = positionItr.next();

									if (movDr.getPositionLevel().toString()
											.equals(lvl.getValue())) {
										request.setAttribute("positionleveldr",
												lvl.getLabel());
									}
								}
							} else {
								// set attribute for position level
								request.setAttribute("positionleveldr", "");
							}
							// Get the misc params form cache manager
							collMiscParams = CacheManager.getInstance()
									.getMiscParams("PREDICTSTATUS", entityIdDr);

							// Iterate the misc params collection
							itrMiscParams = collMiscParams.iterator();

							while (itrMiscParams.hasNext()) {
								mParams = (itrMiscParams.next());

								if ((movDr.getPredictStatus() != null)
										|| (!"".equals(movDr.getPredictStatus()))) {
									if (movDr.getPredictStatus().equals(
											mParams.getId().getKey2())) {
										// Set the attribute for predict status
										request.setAttribute("predictstatusdr",
												mParams.getParValue());
										break;
									} else {
										request.setAttribute("predictstatusdr",
												"");
									}
								}
							}
							// get the collection form miscparams
							collMiscParamsExternal = (Collection) CacheManager
									.getInstance().getMiscParams(
											"PREDICTSTATUS", entityIdDr);
							itrMiscParamsExt = collMiscParamsExternal
									.iterator();

							while (itrMiscParamsExt.hasNext()) {
								mParams = (itrMiscParamsExt.next());

								if ((movDr.getExtBalStatus() != null)
										|| (!"".equals(movDr.getExtBalStatus()))) {
									if (movDr.getExtBalStatus().equals(
											mParams.getId().getKey2())) {
										request.setAttribute(
												"externalstatusdr", mParams
														.getParValue());
										break;
									} else {
										request.setAttribute(
												"externalstatusdr", "");
									}
								}
							}

						}

						// For credit account
						
						
						if(SwtUtil.isEmptyOrNull(dbLink)) {
							movDetailsCr = sweepsearchManager.fetchMovementDetails(
									hostId, entityIdCr, movementIdCr);
						}else {
							 MovementManager movManger = (MovementManager) SwtUtil.getBean("movementManager");
							 Movement movementCr = movManger.getArchiveMovementDetails(entityIdCr, movementIdCr, archiveId);
							 
							 if(movementCr != null) {
								 movDetailsCr = new ArrayList<>();
								 movDetailsCr.add(movementCr);
							 }
							
						}
						
						
						// Iterate the movement details
						creditItr = movDetailsCr.iterator();
						// instance for movement
						mvmtCr = new Movement();
						while (creditItr.hasNext()) {
							mvmtCr = creditItr.next();
							request.setAttribute("bookcodecr", (mvmtCr
									.getBookCode() != null) ? mvmtCr
									.getBookCode() : "");
							request.setAttribute("matchidcr", (mvmtCr
									.getMatchId() != null) ? mvmtCr
									.getMatchId().toString() : "");

							if (mvmtCr.getPositionLevel() != null) {
								// Get the entity position level
								Collection collPositionLvlNames = SwtUtil
										.getSwtMaintenanceCache()
										.getEntityPositionLevelObjectLVL(
												entityIdCr);
								// Iterate the postion level
								positionItr = collPositionLvlNames.iterator();

								while (positionItr.hasNext()) {
									LabelValueBean lvl = (LabelValueBean) positionItr
											.next();

									if (mvmtCr.getPositionLevel().toString()
											.equals(lvl.getValue())) {
										request.setAttribute("positionlevelcr",
												lvl.getLabel());
									}
								}
							} else {
								// set the attribute for positionlevelcr
								request.setAttribute("positionlevelcr", "");
							}
							// Get the misc param collection from cache manager
							collMiscParams = (Collection) CacheManager
									.getInstance().getMiscParams(
											"PREDICTSTATUS", entityIdCr);
							// iterate the MiscParams collection
							itrMiscParams = collMiscParams.iterator();

							while (itrMiscParams.hasNext()) {
								mParams = (MiscParams) (itrMiscParams.next());
								if ((mvmtCr.getPredictStatus() != null)
										|| (!"".equals(mvmtCr.getPredictStatus()))) {
									if (mvmtCr.getPredictStatus().equals(
											mParams.getId().getKey2())) {
										request.setAttribute("predictstatuscr",
												mParams.getParValue());
										break;
									} else {
										request.setAttribute("predictstatuscr",
												"");
									}
								}
							}
							// Get the collection form cache manager
							collMiscParamsExternal = CacheManager.getInstance()
									.getMiscParams("PREDICTSTATUS", entityIdCr);
							// iterate the MiscParams_ext
							itrMiscParamsExt = collMiscParamsExternal
									.iterator();

							while (itrMiscParamsExt.hasNext()) {
								mParams = (MiscParams) (itrMiscParamsExt.next());
								if ((mvmtCr.getExtBalStatus() != null)
										|| (!"".equals(mvmtCr.getExtBalStatus()))) {
									if (mvmtCr.getExtBalStatus().equals(
											mParams.getId().getKey2())) {
										request.setAttribute(
												"externalstatuscr", mParams
														.getParValue());
										break;
									} else {
										// set the attribute for
										// externalstatuscr
										request.setAttribute(
												"externalstatuscr", "");
									}
								}
							}

						}
					}
				} else {
					// set the attribute for ifNosweepDetailsPresent
					request.setAttribute("ifNosweepDetailsPresent", "true");

				}
			}
			// set attribute for method name
			request.setAttribute("methodName", "populate");
			log.debug(this.getClass().getName() + "- [populate] - Exit ");
			return ("view");
		} catch (SwtException swtExp) {
			swtExp.printStackTrace();
			log.error(this.getClass().getName() + " - populate(). Exception : "
					+ swtExp.getMessage());
			putEntityListInReq(request);

			// Initializing all values to null
			request.setAttribute("currencydetails", new ArrayList());
			request.setAttribute("accountdetails", new ArrayList());
			request.setAttribute("gendetails", new ArrayList());
			request.setAttribute("subdetails", new ArrayList());
			request.setAttribute("authdetails", new ArrayList());
			request.setAttribute("msgdetails", new ArrayList());

			saveErrors(request, SwtUtil.logException(swtExp, request, ""));

			return ("view");

		} catch (Exception e) {
			e.printStackTrace();
			log.error(this.getClass().getName() + " - populate(). Exception : "
					+ e.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(e, "populate",
							SweepSearchAction.class), request, ""));

			return ("fail");

		} finally {
			// nullify the objects
			accountIdCr = null;
			accountIdDr = null;
			entityIdCr = null;
			entityIdDr = null;
//			entityId = null;
			sweep = null;
			sweepSearch = null;
			hostId = null;
			sweepCutOff = null;
			movDetails = null;
			movDr = null;
			collMiscParams = null;
			collMiscParamsExternal = null;
			movDetailsCr = null;
			sweepDetails = null;
			movementIdDr = null;
			movementIdCr = null;
			mvmtCr = null;
			simpleDateFormat = null;
			debitItr = null;
			itrMiscParamsExt = null;
			creditItr = null;
			itrMiscParams = null;
			positionItr = null;
			roleId = null;
			mParams = null;
			entityUserAccessList = null;
			entityUserAccessItr = null;
			entityUserAccess = null;
			currEntityAccess = null;
			sweepId = null;
			sweepDetailsItr = null;
		}
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	private String putCurrencyGroupListInReq(HttpServletRequest request,
			String hostId, String entityId, boolean withAll)
			throws SwtException {
		log
				.debug("Inside PreAdviceInputAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String defaultCurrencyGroup = "";

		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		Collection groupList = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupViewORFullAcess(roleId, entityId);

		if (withAll) {
			currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
		} else {
			currencyGroupList.add(new LabelValueBean("", ""));
		}

		Iterator itGroupList = groupList.iterator();

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		log
				.debug("exiting 'putCurrencyGroupListInReq' method : Returning List of size -> "
						+ currencyGroupList.size());

		if ((currencyGroupList != null) && (currencyGroupList.size() > 0)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(0);
			defaultCurrencyGroup = labelValueBean.getValue();
		}

		log.debug("Returning default Currency group : " + defaultCurrencyGroup);

		return defaultCurrencyGroup;
	}

	/**
	 * This method is used to put associated CurrencyGroup list without defaultCurrencyGroup into the request
	 * that is further displayed on the UI.
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	private Collection putCurrencyGroupList(HttpServletRequest request,
			String hostId, String entityId, boolean withAll)
			throws SwtException {
		log
				.debug("Inside PreAdviceInputAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String defaultCurrencyGroup = "";

		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		Collection groupList = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupViewORFullAcess(roleId, entityId);

		if (withAll) {
			currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
		} else {
			currencyGroupList.add(new LabelValueBean("", ""));
		}

		Iterator itGroupList = groupList.iterator();

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		log.debug("exiting 'putCurrencyGroupListInReq' method : Returning List of size -> "
						+ currencyGroupList.size());

		if ((currencyGroupList != null) && (currencyGroupList.size() > 0)) {
			ArrayList tempArrayList = (ArrayList) currencyGroupList;
			LabelValueBean labelValueBean = (LabelValueBean) tempArrayList
					.get(0);
			defaultCurrencyGroup = labelValueBean.getValue();
		}

		log.debug("Returning default Currency group : " + defaultCurrencyGroup);

		return currencyGroupList;
	}

	/**
	 * This method is used to put associated CurrencyCode list into the request
	 * that is further displayed on the UI.
	 * 
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */
	private String putCurrencyListInReq(HttpServletRequest request,
			String hostId, String entityId, String currencyGroup)
			throws SwtException {
		log.debug("Inside PreAdviceInputAction.putCurrencyListInReq method");

		Collection currencyDropDown = new ArrayList();
		String defaultCurrency = "";

		String roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		// List of CurrencyTo objects
		ArrayList currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVLForGroup(entityId, currencyGroup);

		if (currencyList != null) {
			currencyList.remove(new LabelValueBean("Default", "*"));
		}

		if (currencyList != null) {
			Iterator itr = currencyList.iterator();

			while (itr.hasNext()) {
				LabelValueBean lb = (LabelValueBean) (itr.next());
				String label = lb.getLabel();
				String value = lb.getValue();

				if (SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId,
						entityId, value)) {
					currencyDropDown.add(new LabelValueBean(value, value));
				}
			}
		}

		request.setAttribute("currencydetails", currencyDropDown);

		if ((currencyDropDown != null) && (currencyDropDown.size() > 0)) {
			LabelValueBean lb = (LabelValueBean) currencyDropDown.iterator()
					.next();
			defaultCurrency = lb.getValue();
		}

		log.debug("exiting 'putCurrencyListInReq' method");

		return defaultCurrency;
	}
	/**
	 * 
	 * This function returns collection of currencies
	 * 
	 * @param HttpServletRequest
	 *            request
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @return - collection of currencies
	 * @throws SwtException -
	 *             SwtException object
	 */
	private Collection getCurrencyList(HttpServletRequest request,
			String hostId, String entityId) throws SwtException {

		/* Method's local variable declaration */
		String roleId = "";
		ArrayList currencyList = null;
		Collection currrencyListWithAll = null;

		log
				.debug(this.getClass().getName()
						+ " - [ getCurrencyList ] - Entry ");

		/* Getting the User's Role Id from the session object */
		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		/* Returns the currency Access List based on the Role */
		currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);

		/* Check for currency List not NULL */
		if (currencyList != null) {

			/*
			 * Removes the LabelValueBean object for the Key as 'Default' from
			 * the collection
			 */
			currencyList.remove(new LabelValueBean("Default", "*"));

			/* Assigning the new ArrayList object to a new Collection Object */
			currrencyListWithAll = new ArrayList();

			/*
			 * Adding a new LabelValueBean object with the Key as 'ALL' and
			 * value as 'ALL'
			 */
			currrencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));

			/* Adding the currencyList object to collection object */
			currrencyListWithAll.addAll(currencyList);
		}

		log.debug(this.getClass().getName() + " - [ getCurrencyList ] - Exit ");
		return currrencyListWithAll;
	}

	/**
	 * This method is used to check the sweep id is valid or not
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	public String checkSweepId()
			throws Exception {
		/* variable to hold the hostId */
		String hostId = null;
		/* variable to hold the sweep id */
		String sweepId = null;
		/* variable to hold the sweep data */
		Long sweepData = null;
		/* Variable to hold sweep details */
		Collection sweepDetails = null;
		//getting archive ID
		String archiveId = null;
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		try {
			log.debug(this.getClass().getName()
					+ "- [checkSweepId] - Entering ");

			/* Getting the hostId from Swt util file */
			hostId = SwtUtil.getCurrentHostId();
			ArchiveManager achiveManager = (ArchiveManager) (SwtUtil
					.getBean("archiveManager"));
			/* Read the sweep id from request */
			sweepId = request.getParameter("sweepId");
			/* Read the sweep id from request */
			archiveId = request.getParameter("archiveId");
			/* Trimming the unwanted spaces */
			sweepData = new Long(sweepId.trim());
			/* Fetches sweep details by calling manager class */
			if(SwtUtil.isEmptyOrNull(archiveId)) {
				sweepDetails = sweepsearchManager.fetchsweepdetails(sweepData,
						hostId);
				
			if (sweepDetails.size() == 0) {

					Archive arch = achiveManager.getCurrentArchiveDb(hostId);
					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepData, hostId, arch.getId().getArchiveId());
					if (sweepDetails.size() > 0) {						
						request.setAttribute("archiveId", arch.getId().getArchiveId());
					}
			}

			}else {

			//	String dbLink = achiveManager.getDBlink(archiveId);
				sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepData, hostId, archiveId);
				
			}

			/* Condition to check the collection size and set flag */
			if (sweepDetails.size() == 0) {
				/* Response the value as false */
				response.getWriter().print(false);
			} else {
				/* Response the value as true */
				response.getWriter().print(true);
			}

			log
					.debug(this.getClass().getName()
							+ "- [checkSweepId] - Exiting ");
		} catch (SwtException swtexp) {
			SwtUtil.logException(swtexp, request, "");
			log.error(this.getClass().getName()
					+ "- [checkSweepId] - SwtException " + swtexp.getMessage());
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [checkSweepId] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkSweepId", SweepSearchAction.class), request, "");
			response.getWriter().print(exp.getMessage());
		} finally {
			hostId = null;
			sweepId = null;
			sweepDetails = null;
			sweepData = null;
		}

		return null;
	}

	public String SweepGetGrid() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();


		// Archive Id
		String archiveId = null;
		// Entity Id
		String entityId = null;
		// Amount Over
		String amountover = null;
		// Amount Under
		String amountunder = null;
		// Currency Code
		String currencyCode = null;
		// Currency Group
		String currencyGroup = null;
		// Account Id
		String accountId = null;
		// Book Code
		String bookCode = null;
		// Value From Date As String
		String valueFromDateAsString = null;
		// Value To Date As String
		String valueToDateAsString = null;
		// Generated by
		String generatedby = null;
		// Postcut Off
		String postcutoff = null;
		// submittedby
		String submittedby = null;
		// authorisedby
		String authorisedby = null;
		// accounttype
		String accounttype = null;
		// sweeptype 
		String sweeptype = null;
		// status
		String status = null;
		// msgFormat
		String msgFormat = null;
		
		try {
			log.debug(this.getClass().getName() + "- [SweepGetGrid] - Entering ");
			request.setAttribute("archiveId", request.getParameter("archiveId"));
			request.setAttribute("entityId", request.getParameter("entityId"));
			request.setAttribute("amountover", request.getParameter("amountover"));
			request.setAttribute("amountunder", request.getParameter("amountunder"));
			request.setAttribute("currencyCode", request.getParameter("currencyCode"));
			request.setAttribute("currencyGroup", request.getParameter("currencyGroup"));
			request.setAttribute("accountId", request.getParameter("accountId"));
			request.setAttribute("bookCode", request.getParameter("bookCode"));
			request.setAttribute("valueFromDateAsString", request.getParameter("valueFromDateAsString"));
			request.setAttribute("valueToDateAsString", request.getParameter("valueToDateAsString"));
			request.setAttribute("generatedby", request.getParameter("generatedby"));
			request.setAttribute("postcutoff", request.getParameter("postcutoff"));
			request.setAttribute("submittedby", request.getParameter("submittedby"));
			request.setAttribute("authorisedby", request.getParameter("authorisedby"));
			request.setAttribute("accounttype", request.getParameter("accounttype"));
			request.setAttribute("sweeptype", request.getParameter("sweeptype"));
			request.setAttribute("status", request.getParameter("status"));
			request.setAttribute("msgFormat", request.getParameter("msgFormat"));
			log.debug(this.getClass().getName() + "- [SweepGetGrid] - Exiting ");
			return ("SweepGetGrid");

		} catch (Exception e) {
			log.error("Exception Catch in SweepSearchAction.'SweepGetGrid' method : " + e.getMessage());
			SwtUtil.logErrorInDatabase(SwtErrorHandler.getInstance().handleException(e, "SweepGetGrid",
					SweepSearchAction.class));
			return ("fail");
		}
	}
	
	/**
	 * This method is used to display the Sweep details. Based on the access get
	 * the details and further displayed on the UI.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String getSweepGrid()
			throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		
		/* Method's local variable and class instance declaration */
		
		// variable to hold hostId
		String hostId = null;
		// variable to hold amountOver
		Double amountOver = null;
		// variable to hold amountUnder
		Double amountUnder = null;
		// variable to hold sweepSearch
		Sweep sweepSearch = new Sweep();
		// variable to hold entityId
		String entityId = null;
		// variable to hold status
		String status = null;
		// variable to hold type
		String type = null;
		// variable to hold accType
		String accType = null;
		// variable to hold currencyCode
		String currencyCode = null;
		// variable to hold currencyGroup
		String currencyGroup = null;
		// variable to hold accounttype
		String accounttype = null;
		// variable to hold accountId
		String accountId = null;
		// variable to hold amountOverAsString
		String amountOverAsString = null;
		// variable to hold amountunderAsString
		String amountunderAsString = null;
		// variable to hold search list
		Collection<Sweep> searchlist = null;
		// variable to hold currencyCodeArray
		Object[] currencyCodeArray = null;
		// variable to hold itrSearchList
		Iterator<Sweep> itrSearchList = null;
		// variable to hold sweep
		Sweep sweep = null;
		// variable to hold currList
		Collection<LabelValueBean> currencyList = null;
		// variable to hold curryList
		List<LabelValueBean> currList = null;
		
		String archiveId = null; 
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String defaultEntity = null;
		

		
		try {
			log.debug(this.getClass().getName() + " - [getSweepGrid] - Entering");
			// Get the host id from cache manager
			hostId = CacheManager.getInstance().getHostId();
			// Get the status from request
			status = request.getParameter("status");
			sweepSearch.setSweepStatus(status);
			// Get the type from request
			type = request.getParameter("sweeptype");
			sweepSearch.setSweepType(type);
			// Get the accType from request
			accType = request.getParameter("accounttype");
			sweepSearch.setAccType(request.getParameter("accounttype"));
			// Get the entityId from request
			entityId = request.getParameter("entityId");
			sweepSearch.setEntityId(entityId);
			// set the entity id
			if (entityId != null) {
				sweepSearch.setEntityId(entityId);
			}
			// Get the currencyCode from request
			currencyCode = request.getParameter("currencyCode");
			sweepSearch.setCurrencyCode(currencyCode);
			// Get the currencyGroup from request
			currencyGroup = request.getParameter("currencyGroup");
			sweepSearch.setCurrencyGroup(currencyGroup);
			// Get the currencyGroup from request
			archiveId = request.getParameter("archiveId");
			sweepSearch.setArchiveId(archiveId);
			// Get the accountId from request
			accountId = request.getParameter("accountId");
			sweepSearch.setAccountId(accountId);
			// Get the amountOverAsString from request
			amountOverAsString = request.getParameter("amountover");
			sweepSearch.setAmountoverAsString(amountOverAsString);
			// Get the amountunderAsString from request
			amountunderAsString = request.getParameter("amountunder");
			sweepSearch.setAmountunderAsString(amountunderAsString);
			// Get the over and under amount form sweep search
			if (amountOverAsString.equals("")) {
				sweepSearch.setAmountover(new Double(0.0));
				amountOver = sweepSearch.getAmountover();
			} else {
				sweepSearch.setAmountover(SwtUtil.parseCurrency(
						amountOverAsString, SwtUtil.getCurrentSystemFormats(
								request.getSession()).getCurrencyFormat()));
				amountOver = sweepSearch.getAmountover();
			}
			// get the aumount under from sweep search
			if (amountunderAsString.equals("")) {
				sweepSearch.setAmountunder(new Double(0.0));
				amountUnder = sweepSearch.getAmountunder();
			} else {
				sweepSearch.setAmountunder(SwtUtil.parseCurrency(
						amountunderAsString, SwtUtil.getCurrentSystemFormats(
								request.getSession()).getCurrencyFormat()));
				amountUnder = sweepSearch.getAmountunder();
			}

			// Setting the values for account id
			if (SwtUtil.isEmptyOrNull(accountId)) {
				sweepSearch.setAccountId("");
			} else {
				sweepSearch.setAccountId(accountId);
			}
			// Setting the values for currency code
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				sweepSearch.setCurrencyCode("");
			} else {
				sweepSearch.setCurrencyCode(currencyCode);
			}
			// Setting the values for currency group
			if (SwtUtil.isEmptyOrNull(currencyGroup)) {
				sweepSearch.setCurrencyGroup("");
			} else {
				sweepSearch.setCurrencyGroup(currencyGroup);
			}
			// put the currency group list in request
//			putCurrencyGroupListInReq(request, hostId, entityId, false);
			
			/*
			 * Start:code modified by sandeepkumar for Issue Found On STL Testing:1054_STL_002 on 22-mar-12:
			 * system should not allow to display No access currency group
			 * records in the "Sweep search results" screen.
			 * 
			 */
			// get the currency code array detials
			currencyCodeArray = getCurrencyCodeArray(request, hostId, entityId,
					currencyGroup, currencyCode);
			/*
			 * Start:code modified by sandeepkumar for  Issue Found On STL Testing:1054_STL_002 on 22-mar-12:
			 * system should not allow to display No access currency group
			 * records in the "Sweep search results" screen.
			 */
			// Get the search list form manager
			searchlist = sweepsearchManager.fetchdetails(request
					.getParameter("sortorder"), request
					.getParameter("accounttype"), status, type, currencyCode,
					request.getParameter("msgFormat"), accountId, request
							.getParameter("bookCode"), request
							.getParameter("generatedby"), request
							.getParameter("authorisedby"), request
							.getParameter("submittedby"), entityId, hostId,
					request.getParameter("postcutoff"), amountOver,
					amountUnder, request.getParameter("valueFromDateAsString"),
					request.getParameter("valueToDateAsString"), SwtUtil
							.getCurrentSystemFormats(request.getSession()),
					currencyCodeArray, archiveId);

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.SWEEP_SEARCH_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
	
			
			// iterate the search list
			itrSearchList = searchlist.iterator();
			// instance for sweep
			sweep = new Sweep();
			// To display the amount,user according to the status
			while (itrSearchList.hasNext()) {
				sweep = itrSearchList.next();

				if (sweep.getOriginalSweepAmt() != null) {
					sweep.setOriginalSweepAmtasstring(SwtUtil.formatCurrency(
							sweep.getCurrencyCode(), sweep
									.getOriginalSweepAmt()));
				}

				if (sweep.getSubmitSweepAmt() != null) {
					sweep
							.setSubmitSweepAmtasstring(SwtUtil.formatCurrency(
									sweep.getCurrencyCode(), sweep
											.getSubmitSweepAmt()));

					sweep.setNewCalulatedAmount(sweep
							.getSubmitSweepAmtasstring());

				}

				if (sweep.getAuthorizeSweepAmt() != null) {
					sweep.setAuthorizeSweepAmtasstring(SwtUtil.formatCurrency(
							sweep.getCurrencyCode(), sweep
									.getAuthorizeSweepAmt()));

					sweep.setNewCalulatedAmount(sweep
							.getAuthorizeSweepAmtasstring());

				}

				if ((status.equals(SwtConstants.SWEEP_STATUS_NEW))
						&& ((type.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
								|| (type.equals(SwtConstants.SWEEP_TYPE_MANUAL)) || (type
								.equals(SwtConstants.SWEEP_TYPE_AUTO)))) {
					sweep.setAmt(sweep.getOriginalSweepAmtasstring());
				} else if ((status.equals(SwtConstants.SWEEP_STATUS_STP))
						&& ((type.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
								|| (type.equals(SwtConstants.SWEEP_TYPE_MANUAL)) || (type
								.equals(SwtConstants.SWEEP_TYPE_AUTO)))) {

					sweep.setAmt(sweep.getOriginalSweepAmtasstring());

				} else if ((status.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
						&& ((type.equals(SwtConstants.SWEEP_STATUS_SUBMIT))
								|| (type.equals(SwtConstants.SWEEP_TYPE_MANUAL)) || (type
								.equals(SwtConstants.SWEEP_TYPE_AUTO)))) {
					sweep.setAmt(sweep.getOriginalSweepAmtasstring());
				} else {
					sweep.setAmt(sweep.getOriginalSweepAmtasstring());
				}

				// Setting Value of Sweep Status
				if (status.equals(SwtConstants.SWEEP_STATUS_NEW)) {
					sweep.setUser(sweep.getInputUser());
				} else if (status.equals(SwtConstants.SWEEP_STATUS_STP)) {
					sweep.setUser(sweep.getSubmitUser());
				} else if (status.equals(SwtConstants.SWEEP_STATUS_SUBMIT)) {
					sweep.setUser(sweep.getAuthorizedUser());
				} else if (status.equals(SwtConstants.BOOK)) {
					sweep.setUser(sweep.getCancelUser());
				} else {
					if ((sweep.getSweepStatus()
							.equals(SwtConstants.SWEEP_STATUS_NEW))
							|| (sweep.getSweepStatus()
									.equals(SwtConstants.SWEEP_STATUS_STP))) {
						sweep.setUser(sweep.getInputUser());
					}

					if (sweep.getSweepStatus().equals(
							SwtConstants.SWEEP_STATUS_SUBMIT)) {
						sweep.setUser(sweep.getSubmitUser());
					}

					if (sweep.getSweepStatus().equals(
							SwtConstants.SWEEP_STATUS_AUTHORIZE)) {
						sweep.setUser(sweep.getAuthorizedUser());
					}

					if (sweep.getSweepStatus().equals(
							SwtConstants.SWEEP_STATUS_CANCEL)) {
						sweep.setUser(sweep.getCancelUser());
					}
				}

				if (sweep.getSweepStatus()
						.equals(SwtConstants.SWEEP_STATUS_NEW)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_NEW);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_SUBMIT)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_SUBMITTED);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_CANCEL)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_CANCELLED);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_STP)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_STP);
				} else if (sweep.getSweepStatus().equals(
						SwtConstants.SWEEP_STATUS_AUTHORIZE)) {
					sweep.setSweepStatus(SwtConstants.SWEEP_AUTHORIZED);
				}

				// Setting Type in list screen
				if (sweep.getSweepType() != null) {
					if (sweep.getSweepType().equals(
							SwtConstants.SWEEP_TYPE_AUTO)) {
						sweep.setSweepType("Auto");
					} else if (sweep.getSweepType().equals(
							SwtConstants.SWEEP_TYPE_MANUAL)) {
						sweep.setSweepType("Manual");
					}
				}

			}
					
			// set attribute for search list
			request.setAttribute("searchdisplay", searchlist);
			// checks the account typeo
			if (accType.equals(SwtConstants.ACCOUNT_TYPE_CASH)) {
				// set the account type to sweep search
				sweepSearch.setAccType("Cash");
			} else if (accType.equals(SwtConstants.ACCOUNT_TYPE_CUSTODIAN)) {
				// set the account type to sweep search
				sweepSearch.setAccType("Custodian");
			} else {
				sweepSearch.setAccType(SwtConstants.ALL_VALUE);
			}
			// checks the currency code is empty or null
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				sweepSearch.setCurrencyCode(SwtConstants.ALL_VALUE);
				// get the currency details from manager
				currList = sweepsearchManager.getCurrencyDetails(hostId,
						entityId);
				// add the label value bean to currlist
				currList.add(0, new LabelValueBean("", SwtConstants.ALL_VALUE));
				// set the currency details in attribute
				request.setAttribute("currencydetails", currList);
			} else {
				// get the currency details form manager
				currencyList = sweepsearchManager.getCurrencyDetails(hostId,
						entityId);
				// set the currency details in attribute
				request.setAttribute("currencydetails", currencyList);
			}
			// set attribute for method name
			request.setAttribute("methodName", "view");
			// set attribute for screen field status
			request.setAttribute("screenFieldsStatus", "true");
			// set attribute for screen field archiveId
			request.setAttribute("archiveId", archiveId);
			
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			
			responseConstructor.createElement("archiveId", sweepSearch.getArchiveId());
			responseConstructor.createElement("entityId", sweepSearch.getEntityId());
			responseConstructor.createElement("currencyCode", sweepSearch.getCurrencyCode());
			responseConstructor.createElement("valueFromDateAsString", sweepSearch.getValueFromDateAsString());
			responseConstructor.createElement("postcutoff", sweepSearch.getPostcutoff());
			responseConstructor.createElement("accounttype", sweepSearch.getAccType());
			responseConstructor.createElement("sweeptype", sweepSearch.getSweepType());
			responseConstructor.createElement("status", sweepSearch.getSweepStatus());
			responseConstructor.createElement("methodName","view");
			responseConstructor.createElement("screenFieldsStatus", "true");
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
			
			/***** Account list Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			HttpSession session = request.getSession();
			Collection coll = SwtUtil.getUserEntityAccessList(session);
			coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
			Iterator j = coll.iterator();
			LabelValueBean row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
//				lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE, SwtConstants.ALL_ENTITY_VALUE, false));
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Account list Combo End ***********/
			
			/***** Currency Group Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			 row = null;
			 Collection currencyGroupList = putCurrencyGroupList(request, hostId, entityId, false);
			 j = currencyGroupList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyGroupList", lstOptions));	
			/***** Currency Group Combo End ***********/
			
			responseConstructor.formSelect(lstSelect);
			
			/******* SweepSearchList ******/
			responseConstructor.formGridStart("SweepSearchListGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			// form rows (records)
			responseConstructor.formRowsStart(searchlist.size());
			for (Iterator<Sweep> it = searchlist.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Sweep searchlistrecord = (Sweep) it.next();
				responseConstructor.formRowStart();

				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME , searchlistrecord.getValueDateAsString() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME,searchlistrecord.getCurrencyCode() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME,searchlistrecord.getAmt());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME, searchlistrecord.getNewCalulatedAmount() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME,searchlistrecord.getEntityIdCr());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME, searchlistrecord.getAccountIdCr());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_TAGNAME, searchlistrecord.getAccountCr().getAcctname()); 
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME, searchlistrecord.getEntityIdDr());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME, searchlistrecord.getAccountIdDr());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME, searchlistrecord.getAccountDr().getAcctname());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME, ""+searchlistrecord.getId().getSweepId());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME, searchlistrecord.getAccountCr().getAcctNewCrInternal());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME,searchlistrecord.getAccountCr().getAcctNewCrExternal() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME,searchlistrecord.getAccountDr().getAcctNewDrInternal());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME, searchlistrecord.getAccountDr().getAcctNewDrExternal());				
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME,searchlistrecord.getSweepType() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME,searchlistrecord.getUser());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME, searchlistrecord.getSweepStatus());
				
				//hidden property
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_SUBMIT_USER_TAGNAME , searchlistrecord.getSubmitUser() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_AUTHORIZED_USER_TAGNAME,searchlistrecord.getAuthorizedUser() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_MOVEMENTID_DR_TAGNAME,searchlistrecord.getMovementIdDr());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_MOVEMENTID_CR_TAGNAME, searchlistrecord.getMovementIdCr() );
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_ORIGINAL_SWEEP_AMT_ASSTRING_TAGNAME,searchlistrecord.getOriginalSweepAmtasstring());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_SUBMIT_SWEEP_AMT_ASSTRING_TAGNAME, searchlistrecord.getSubmitSweepAmtasstring());
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_AUTHORIZE_SWEEP_AMT_ASSTRING_TAGNAME, searchlistrecord.getAuthorizeSweepAmtasstring()); 
				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_ID_TAGNAME, searchlistrecord.getEntityId());
//				responseConstructor.createRowElement(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME,searchlistrecord.getAmt());				
				
				responseConstructor.formRowEnd();
			}
		
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();
			
			xmlWriter.endElement(SwtConstants.SWEEP_SEARCH_LIST);
			request.setAttribute("data", xmlWriter.getData());
						
			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [getSweepGrid] - Exiting");
			return ("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			log.error(this.getClass().getName()
					+ " - getSweepGrid(). SwtException : " + swtexp.getMessage());
			return ("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "getSweepGrid", SweepSearchAction.class), request, "");
			log.error(this.getClass().getName() + " - getSweepGrid(). Exception : "
					+ e.getMessage());
			return ("fail");
		} 
		finally {
			// nullify objects
			hostId = null;
			amountOver = null;
			amountUnder = null;
			sweepSearch = null;
			entityId = null;
			status = null;
			type = null;
			accType = null;
			currencyCode = null;
			currencyGroup = null;
			accountId = null;
			amountOverAsString = null;
			amountunderAsString = null;
			currencyCodeArray = null;
			itrSearchList = null;
			sweep = null;
			currencyList = null;
			currList = null;
		}
	}
	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
  			HttpServletRequest request) throws SwtException {
  		// Array list to hold column order
  		ArrayList<String> orders = null;
  		// String array variable to hold column order property
  		String[] columnOrderProp = null;
  		// Iterator variable to hold column order
  		Iterator<String> columnOrderItr = null;
  		// Hash map to hold column width
  		LinkedHashMap<String, String> widths = null;
  		// String array variable to hold width property
  		String[] columnWidthProperty = null;
  		// List for column info
  		List<ColumnInfo> lstColumns = null;
  		/* Hash map to hold column hidden_Columns */
  		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
  		/* Array list to hold hidden Column array */
  		ArrayList<String> lstHiddenColunms = null;
  		/* String array variable to hold hidden columns property */
  		String[] hiddenColumnsProp = null;
  		try {
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
  			// Condition to check width is null

  			if (SwtUtil.isEmptyOrNull(width)) {
  				// default width for columns
				width =   SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME + "=80" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME + "=67" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME + "=165" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME + "=165" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME + "=100" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME + "=220" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_TAGNAME + "=180" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME + "=100" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME + "=220" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME + "=180" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME + "=120" + ","
				        + SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME + "=140"+","
				        + SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME + "=140" + ","
				        + SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME + "=140" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME + "=140" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME + "=176" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME + "=144" + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME + "=98";

   			}

  			// Obtain width for each column
  			columnWidthProperty = width.split(",");

  			// Loop to insert each column in hash map
  			widths = new LinkedHashMap<String, String>();
  			for (int i = 0; i < columnWidthProperty.length; i++) {
  				// Condition to check index of = is -1
  				if (columnWidthProperty[i].indexOf("=") != -1) {
  					String[] propval = columnWidthProperty[i].split("=");
  					widths.put(propval[0], propval[1]);
  				}
  			}

  			// Condition to check column order is null or empty
  			if (SwtUtil.isEmptyOrNull(columnOrder)) {
  				// Default values for column order
				columnOrder = SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME + ","
						+ SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME ;
}
  			orders = new ArrayList<String>();
  			// Split the columns using , and save in string array
  			columnOrderProp = columnOrder.split(",");

  			// Loop to enter column order in array list
  			for (int i = 0; i < columnOrderProp.length; i++) {
  				// Adding the Column values to ArrayList
  				orders.add(columnOrderProp[i]);
  				hiddenColumnsMap.put(columnOrderProp[i], true);
  			}

  			/* Condition to check column hidden is null or empty */
  			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
  				lstHiddenColunms = new ArrayList<String>();
  				/* Split the hidden columns , and save in string array */
  				hiddenColumnsProp = hiddenColumns.split(",");

  				for (int i = 0; i < hiddenColumnsProp.length; i++) {
  					/* Adding the Column values to ArrayList */
  					lstHiddenColunms.add(hiddenColumnsProp[i]);
  				}

  				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
  				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
  					String columnKey = listKeys.next();
  					// boolean found = false;
  					for (int j = 0; j < lstHiddenColunms.size(); j++) {
  						if (columnKey.equals(lstHiddenColunms.get(j))) {
  							hiddenColumnsMap.put(columnKey, false);
  							break;
  						}
  					}
  				}
  			}

  			columnOrderItr = orders.iterator();
  			lstColumns = new ArrayList<ColumnInfo>();

  			while (columnOrderItr.hasNext()) {
  				String order = (String) columnOrderItr.next();
 				ColumnInfo tmpColumnInfo = null;
 				
  				// VALUE_DATE column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME, SwtConstants.COLUMN_TYPE_DATE, 0,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_VALUE_DATE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// CURRENCY_CODE column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENCY_CODE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// CURRENT_AMT column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 2,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CURRENT_AMT_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// NEW_AMT column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 3,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NEW_AMT_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// ENTITY_CR column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 4,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_CR_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// ACCOUNTID_CR column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 5,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_TAGNAME)), false,
  							true, true));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_CR_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// NAME_CR column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 6,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_TAGNAME)), false,
  							true, true));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_CR_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// ENTITY_DR column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 7,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ENTITY_DR_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  			   // ACCOUNTID_CR column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 8,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_ACCOUNTID_DR_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// NAME_DR column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 9,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_NAME_DR_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  				// SWEEPID column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME, SwtConstants.COLUMN_TYPE_NUMBER, 10,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPID_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}

 				// CRDINTMSG column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 11,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_TAGNAME)), false,
  							true, true));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDINTMSG_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}		
  				// CRDEXTMSG column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 12,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_TAGNAME)), false,
  							true, true));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_CRDEXTMSG_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}	
  			// DRINTMSG column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 13,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DRINTMSG_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  			// DREXTMSG column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME)) { 
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 14,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_DREXTMSG_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  			// SWEEPTYPE column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 15,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPTYPE_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  			// SWEEPUSER column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 16,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_SWEEPUSER_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}
  			// STATUS1 column
  				if (order.equals(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME)) {
  					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_HEADING, request),
  							SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 17,
  							Integer.parseInt(widths.get(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME)), false,
  							true, hiddenColumnsMap.get(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_TAGNAME)));
  					
  					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.SWEEP_SEARCH_LIST_STATUS1_HEADING_TOOLTIP, request));
  					lstColumns.add(tmpColumnInfo);
  				}

  			}

  		} catch (Exception ex) {
  			// log error message
  			log.error(this.getClass().getName()
  					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

  			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
  					this.getClass());

  		} finally {
  			// Nullify Objects
  			orders = null;
  			columnOrderProp = null;
  			columnOrderItr = null;
  			widths = null;
  			columnWidthProperty = null;
  			// log debug message
  			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
  		}
  		// return XML columns
  		return lstColumns;
  	}
	
	public String OpenSubSweepSearchList() throws SwtException {
HttpServletRequest request = ServletActionContext.getRequest();
HttpServletResponse response = ServletActionContext.getResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("params", request.getParameter("params"));
		return ("view");
	}
	
}
