/*
 * @(#)SweepSearchDAOHibernate.java 1.0 15/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.control.model.Archive;
import org.swallow.control.service.ArchiveManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.Entity;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.SweepSearchDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.service.MovementManager;
import org.springframework.context.annotation.Lazy;
import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.Session;



/**
 * <AUTHOR>
 * 
 * <Pre>
 * DAO layer for sweep search 
 * 
 * Method to used to 
 * - fetch account details
 * - fetch sweep details
 * - get currency details
 * - calculate sweep amount
 * - get AuthorizedDetails,generated,message,book code,position level details
 * 
 * </Pre>
 */
@Repository ("sweepsearchDAO")
public class SweepSearchDAOHibernate extends HibernateDaoSupport implements
		SweepSearchDAO {
	public SweepSearchDAOHibernate(@Lazy SessionFactory sessionfactory) {
	    setSessionFactory(sessionfactory);
	}

	private final Log log = LogFactory.getLog(SweepSearchDAOHibernate.class);

	public Collection getCurrencyDetails(String hostId, String entityId) {
		java.util.List currencyList = getHibernateTemplate()
				.find(
						" from Currency c where c.id.hostId = ?0 and c.id.entityId = ?1 and c.id.currencyCode!='*' order by c.id.currencyCode",
						new Object[] { hostId, entityId });
		return currencyList;
	}

	public Collection getAccountDetails(String hostId, String entityId,
			String currencyCode) {
		java.util.List accountList = getHibernateTemplate()
				.find(
						"from AcctMaintenance ac where ac.id.hostId = ?0 and ac.id.entityId = ?1 and ac.id.accountId != '*' and ac.currcode like ?2 order by ac.id.accountId",
						new Object[] { hostId, entityId, currencyCode });
		return accountList;
	}

	public List getGeneratedDetails(String hostId, String entityId) {
		java.util.List genList = getHibernateTemplate()
				.find(
						"select distinct sweep.inputUser  from Sweep sweep where sweep.id.hostId = ?0 and (sweep.entityIdCr = ?1 or sweep.entityIdDr= ?2) and sweep.inputUser is not null order by sweep.inputUser",
						new Object[] { hostId, entityId, entityId });
		return genList;
	}

	public List getSubmittedDetails(String hostId, String entityId) {
		java.util.List subList = getHibernateTemplate()
				.find(
						"select distinct sweep.submitUser from Sweep sweep where sweep.id.hostId = ?0 and (sweep.entityIdCr = ?1 or sweep.entityIdDr= ?2) and sweep.submitUser is not null order by sweep.submitUser",
						new Object[] { hostId, entityId, entityId });
		return subList;
	}

	public List getAuthorizedDetails(String hostId, String entityId) {
		java.util.List authList = getHibernateTemplate()
				.find(
						"select distinct sweep.authorizedUser from Sweep sweep where sweep.id.hostId = ?0 and (sweep.entityIdCr = ?1 or sweep.entityIdDr= ?2) and sweep.authorizedUser is not null order by sweep.authorizedUser",
						new Object[] { hostId, entityId , entityId});
		return authList;
	}

	public Collection getMessageDetails(String hostId, String entityId) {
		java.util.List msgList = getHibernateTemplate()
				.find(
						"from MessageFormats msg where msg.id.hostId = ?0 and msg.id.entityId = ?1 order by msg.id.formatId",
						new Object[] { hostId, entityId });
		return msgList;
	}

	public Collection getBookCodeDetails(String hostId, String entityId) {
		java.util.List bookList = getHibernateTemplate()
				.find(
						"from BookCode book where book.id.hostId = ?0 and book.id.entityId = ?1 order by book.id.bookCode",
						new Object[] { hostId, entityId });
		return bookList;
	}

	public Collection getPositionLevelDetails(String hostid, String entityid) {
		return null;
	}

	public Collection getSweepDetails(String hostId) {
		java.util.List sweeplist = (List ) getHibernateTemplate().find(
						"from Sweep sweep where sweep.id.hostId = ?0 order by sweep.id.sweepId",
						new Object[] { hostId });
		return sweeplist;
	}

	public Collection fetchsweepdetails(Long sweepid, String hostId) {
		Sweep sweep = new Sweep();
		java.util.List sweeplist = (List ) getHibernateTemplate().find(
				"from Sweep sweep where sweep.id.sweepId = '" + sweepid
						+ "' and sweep.id.hostId='" + hostId + "'");
		return sweeplist;
	}

	public Collection fetchsweepdetailsArchive(Long sweepid, String hostId, String archiveId) {
		Sweep sweep = new Sweep();
		if(SwtUtil.isEmptyOrNull(archiveId)) {
			java.util.List sweeplist = (List ) getHibernateTemplate().find(
					"from Sweep sweep where sweep.id.sweepId = '" + sweepid
					+ "' and sweep.id.hostId='" + hostId + "'");
			return sweeplist;
		}else {
			Session session  = null;
			Connection conn = null;
			ResultSet resultSet = null;
			PreparedStatement statement = null;
			List<Object> pSweepList = new ArrayList<>();
			try {
				String tableName = SwtUtil.getFormattedTableName(hostId, archiveId, "p_sweep");
				String  query = "select * from " + tableName + " where sweep_id = ?";
				session = getHibernateTemplate().getSessionFactory().openSession();
				conn = SwtUtil.connection(session);
				statement = conn.prepareStatement(query);
				statement.setLong(1,sweepid);
				
				statement.execute();
				resultSet = statement.getResultSet();
				
				if (resultSet != null) {
					while (resultSet.next()) {
						 Sweep pSweep = new Sweep();
						 pSweep.getId().setHostId(resultSet.getString("HOST_ID"));
						 pSweep.getId().setSweepId(resultSet.getLong("SWEEP_ID"));
						 pSweep.setSweepGroupId(resultSet.getString("SWEEP_GROUP_ID"));
						 pSweep.setCurrencyCode(resultSet.getString("CURRENCY_CODE"));
						 pSweep.setMovementIdCr(resultSet.getString("MOVEMENT_ID_CR"));
						 pSweep.setMovementIdDr(resultSet.getString("MOVEMENT_ID_DR"));
						 pSweep.setOriginalSweepAmt(resultSet.getObject("ORIGINAL_SWEEP_AMT") != null ? resultSet.getDouble("ORIGINAL_SWEEP_AMT") : null);
						 pSweep.setSubmitSweepAmt(resultSet.getObject("SUBMIT_SWEEP_AMT") != null ? resultSet.getDouble("SUBMIT_SWEEP_AMT") : null);
						 pSweep.setAuthorizeSweepAmt(resultSet.getObject("AUTHORIZE_SWEEP_AMOUNT") != null ? resultSet.getDouble("AUTHORIZE_SWEEP_AMOUNT") : null);
						 pSweep.setSweepType(resultSet.getString("SWEEP_TYPE"));
						 pSweep.setSweepStatus(resultSet.getString("SWEEP_STATUS"));
						 pSweep.setInputDateTime(resultSet.getDate("INPUT_DATE_TIME"));
						 pSweep.setAuthDateTime(resultSet.getDate("AUTHORIZED_DATE_TIME"));
						 pSweep.setInputUser(resultSet.getString("INPUT_USER"));
						 pSweep.setSubmitUser(resultSet.getString("SUBMIT_USER"));
						 pSweep.setAuthorizedUser(resultSet.getString("AUTHORIZED_USER"));
						 pSweep.setCancelUser(resultSet.getString("CANCEL_USER"));
						 pSweep.setAccountIdCr(resultSet.getObject("ACCOUNT_ID_CR") != null ? resultSet.getString("ACCOUNT_ID_CR") : null);
						 pSweep.setAccountIdDr(resultSet.getObject("ACCOUNT_ID_DR") != null ? resultSet.getString("ACCOUNT_ID_DR") : null);
						 pSweep.setAlignAccountId(resultSet.getObject("ALIGN_ACCOUNT_ID") != null ? resultSet.getString("ALIGN_ACCOUNT_ID") : null);
						 pSweep.setValueDate(resultSet.getDate("VALUE_DATE"));
						 pSweep.setUpdateDate(resultSet.getDate("UPDATE_DATE"));
						 pSweep.setUpdateUser(resultSet.getString("UPDATE_USER"));
						 pSweep.setSubmitDateTime(resultSet.getDate("SUBMIT_DATE_TIME"));
						 pSweep.setCancelDateTime(resultSet.getDate("CANCEL_DATE_TIME"));
						 pSweep.setEntityIdCr(resultSet.getObject("ENTITY_ID_CR") != null ? resultSet.getString("ENTITY_ID_CR") : null);
						 pSweep.setEntityIdDr(resultSet.getObject("ENTITY_ID_DR") != null ? resultSet.getString("ENTITY_ID_DR") : null);
						 pSweep.setSettleMethodCR(resultSet.getString("SETTL_METHOD_CR"));
						 pSweep.setSettleMethodDR(resultSet.getString("SETTL_METHOD_DR"));
						 pSweep.setBookCodeCR(resultSet.getString("BOOKCODE_CR"));
						 pSweep.setBookCodeDR(resultSet.getString("BOOKCODE_DR"));
						 pSweep.setAdditionalReference(resultSet.getString("ADDITIONAL_REFERENCE"));
						 pSweep.setTargetBalance(resultSet.getObject("TARGET_BALANCE") != null ? resultSet.getDouble("TARGET_BALANCE") : null);
						 pSweep.setTargetBalanceType(resultSet.getString("TARGET_BALANCE_TYPE"));
						 pSweep.setMinAmount(resultSet.getObject("MIN_AMOUNT") != null ? resultSet.getDouble("MIN_AMOUNT") : null);
						 pSweep.setAccountIdOrigin(resultSet.getObject("ACCOUNT_ID_ORIGIN") != null ? resultSet.getString("ACCOUNT_ID_ORIGIN") : null);
						 pSweep.setEntityIdOrigin(resultSet.getObject("ENTITY_ID_ORIGIN") != null ? resultSet.getString("ENTITY_ID_ORIGIN") : null);
						 pSweep.setSweepFromBalanceTypeCr(resultSet.getString("SWEEP_FROM_BALANCE_TYPE_CR"));
						 pSweep.setSweepFromBalanceTypeDr(resultSet.getString("SWEEP_FROM_BALANCE_TYPE_DR"));
						 pSweep.setTargetBalanceTypId(resultSet.getString("TARGET_BALANCE_TYPE_ID"));
						 
						    
					     pSweepList.add(pSweep);
					}
				}
				
					
					for (int i = 0; i < pSweepList.size(); i++) {
						Sweep pSweep = (Sweep) pSweepList.get(i);
						
			
					String accountCutoffTime = "from AcctMaintenance acc where acc.id.hostId=?0 and acc.id.entityId=?1 and acc.id.accountId=?2 ";
					java.util.List AccListCr = getHibernateTemplate().find(
							accountCutoffTime,
							new Object[] { pSweep.getId().getHostId(), pSweep.getEntityIdCr(), pSweep.getAccountIdCr() });
					java.util.List AccListDr = getHibernateTemplate().find(
							accountCutoffTime,
							new Object[] { pSweep.getId().getHostId(), pSweep.getEntityIdDr(), pSweep.getAccountIdDr() });
				
					AcctMaintenance accountCr = (AcctMaintenance) AccListCr.get(0);
					AcctMaintenance accountDr = (AcctMaintenance) AccListDr.get(0);
				    
				     pSweep.setAccountCr(accountCr);
				     pSweep.setAccountDr(accountDr);
				     
//				     MovementManager movManger = (MovementManager) SwtUtil.getBean("movementManager");
//				     if(!SwtUtil.isEmptyOrNull(pSweep.getMovementIdCr())) {
//				    	 pSweep.set
//				     }
//						
//				     
					
				}
			}catch(Exception e) {
				e.printStackTrace();
			}finally {
				JDBCCloser.close(resultSet,statement,conn,session);
			}
			
			return pSweepList;
		}
	}

	public Collection fetchMovementDetails(String hostid, String entityid,
			Long movementid) {
		java.util.List movlist = getHibernateTemplate().find(
				" from Movement mov where mov.id.hostId ='" + hostid
						+ "' and mov.id.entityId ='" + entityid
						+ "' and mov.id.movementId='" + movementid + "'");
		return movlist;
	}

	/**
	 * This method is used to fetch the sweep search details
	 * 
	 * @param sortOrder
	 * @param acctType
	 * @param status
	 * @param type
	 * @param currency
	 * @param message
	 * @param accountId
	 * @param bookCode
	 * @param inputUser
	 * @param authUser
	 * @param subUser
	 * @param entityId
	 * @param hostId
	 * @param postCutOff
	 * @param amountOver
	 * @param amountUnder
	 * @param fromDate
	 * @param toDate
	 * @param format
	 * @param currencyCodeArray
	 * @param archiveid 
	 * @return
	 * @throws SwtException
	 */
	public Collection fetchdetails(String sortOrder, String acctType,
			String status, String type, String currency, String message,
			String accountId, String bookCode, String inputUser,
			String authUser, String subUser, String entityId, String hostId,
			String postCutOff, Double amountOver,
			Double amountUnder, String fromDate, String toDate, String format,
			Object[] currencyCodeArray, String archiveid) throws SwtException {
		// variable to hold stringBuffer
		StringBuffer stringBuffer = null;
		// variable to hold crQuery
		StringBuffer crQuery = null;
		// variable to hold drQuery
		StringBuffer drQuery = null;
		// variable to hold bookQuery
		StringBuffer bookQuery = null;
		// variable to hold bookList
		List<Sweep> bookList = null;
		// variable to hold over
		double over;
		// variable to hold under
		double under;
		// variable to hold crMsgQuery
		StringBuffer crMsgQuery = null;
		// variable to hold drMsgQuery
		StringBuffer drMsgQuery = null;
		// variable to hold noOfVaraibles
		int noOfVar;
		// variable to hold inputValue
		ArrayList<Date> inputValue = null;
		// variable to hold todayDate
		Date todayDate = null;
		// variable to hold fromDateAsDate
		Date fromDateAsDate = null;
		// variable to hold todateAsDate
		Date todateAsDate = null;
		// variable to hold arrForSwepId
		ArrayList<Object> arrForSwepId = null;
		// variable to hold posQuery
		StringBuffer posQuery = null;
		// variable to hold qryList
		List<Object[]> qryList = null;
		// variable to hold entityList
		ArrayList<Object> entityList = null;
		// variable to hold cutOffList
		ArrayList<Object> cutOffList = null;
		// variable to hold sweepIdsList
		ArrayList<Object> sweepIdsList = null;
		// variable to hold timeList
		ArrayList<Object> timeList = null;
		// variable to hold row
		Object[] row = null;
		// variable to hold iterator
		Iterator<Object[]> iterator = null;
		// variable to hold finalEntityList
		ArrayList<Object> finalEntityList = null;
		// variable to hold finalCuttOffList
		ArrayList<Object> finalCuttOffList = null;
		// variable to hold finalTimeList
		ArrayList<Object> finalTimeList = null;
		// variable to hold finalSweepIdList
		ArrayList<Object> finalSweepIdList = null;
		// variable to hold strCutOff
		String strCutOff = null;
		// variable to hold index
		int index;
		// variable to hold hour
		String hour = null;
		// variable to hold minute
		String minute = null;
		// variable to hold dateGregorian
		Calendar dateGregorian = null;
		// variable to hold dateGre
		Calendar dateGre = null;
		// variable to hold offSetList
		List<Object[]> offSetList = null;
		// variable to hold entities
		String entities = ""; // should be initialized to ""
		// variable to hold result
		Object[] result = null;
		// variable to hold itrOffSet
		Iterator<Object[]> itrOffSet = null;
		// variable to hold strFinalTime
		String strFinalTime = null;
		// variable to hold strResult
		String strResult = null;
		// variable to hold strFinalIndex
		int strFinalIndex;
		// variable to hold strFinalHour
		int strFinalHour;
		// variable to hold strFinalMin
		int strFinalMin;
		// variable to hold strResultIndex
		int strResultIndex;
		// variable to hold strResIndex
		int strResIndex;
		// variable to hold strResultHour
		int strResultHour;
		// variable to hold strResultMin
		int strResultMin;
		// variable to hold offSetTime
		int offSetTime;
		// variable to hold cutOffTime
		int cutOffTime;
		// variable to hold effTime
		int effTime;
		// variable to hold effTimeHour
		int effTimeHour;
		// variable to hold effTimeMin
		int effTimeMin;
		// variable to hold finalTimeInMin
		int finalTimeInMin;
		// variable to hold tempCutOff
		String tempCutOff = null;
		// variable to hold strCutOffIndex
		int strCutOffIndex;
		// variable to hold strCutOffHour
		int strCutOffHour;
		// variable to hold strCutOffMin
		int strCutOffMin;
		// variable to hold tempCutOffInMin
		int tempCutOffInMin;
		// variable to hold postSweepList
		String postSweepList = ""; // should be initialized to ""
		// variable to hold strQuery
		String strQuery = null;
		// variable to hold sweepList
		List<Object> sweepList = null;
		try {
			log
					.debug(this.getClass().getName()
							+ "- [fetchdetails] - Entering");
			// Instance for str buffer
			stringBuffer = new StringBuffer(
					"from Sweep sweep where sweep.id.hostId ='").append(hostId)
					.append("' and (sweep.entityIdCr='").append(entityId)
					.append("' or sweep.entityIdDr='").append(entityId).append(
							"')");
			// checks Account Id is not empty
			if (!accountId.equals("")) {
				stringBuffer.append("and (sweep.accountIdCr='").append(
						accountId).append("' or sweep.accountIdDr='").append(
						accountId).append("')");
			}
			stringBuffer.append("and sweep.currencyCode in ('");
			if (currencyCodeArray != null && currencyCodeArray.length > 0) {
				for (int currencyCounter = 0; currencyCounter < currencyCodeArray.length; currencyCounter++) {
					if (currencyCounter != currencyCodeArray.length - 1) {
						stringBuffer.append(currencyCodeArray[currencyCounter])
								.append("','");
					} else {
						stringBuffer.append(currencyCodeArray[currencyCounter]);
					}
				}
			}
			stringBuffer.append("')");
			// append for Generated by
			if (!inputUser.equals("")) {
				stringBuffer.append("and sweep.inputUser='").append(inputUser)
						.append("'");
			}
			// append for Authorized by
			if (!authUser.equals("")) {
				stringBuffer.append("and sweep.authorizedUser='").append(
						authUser).append("'");
			}
			// append for Submitted by
			if (!subUser.equals("")) {
				stringBuffer.append("and sweep.submitUser='").append(subUser)
						.append("'");
			}
            crQuery = new StringBuffer(
                    "select distinct sweep.movementIdCr from Sweep sweep where sweep.id.hostId='")
                    .append(hostId).append("' and (sweep.entityIdCr='")
                    .append(entityId).append("' or sweep.entityIdDr='")
                    .append(entityId).append("') ");
            
            
            crQuery.append("AND sweep.movementIdCr IN (select mov.id.movementId from Movement mov where mov.id.movementId=sweep.movementIdCr and mov.id.hostId ='" + hostId + "' and mov.id.entityId = sweep.entityIdCr)");
            
            drQuery = new StringBuffer(
                    "select distinct sweep.movementIdDr from Sweep sweep where sweep.id.hostId='")
                    .append(hostId).append("' and (sweep.entityIdCr='")
                    .append(entityId).append("' or sweep.entityIdDr='")
                    .append(entityId).append("') ");
            
            drQuery.append("AND sweep.movementIdDr IN (select mov.id.movementId from Movement mov where mov.id.movementId=sweep.movementIdDr and mov.id.hostId ='" + hostId + "' and mov.id.entityId = sweep.entityIdDr)");

            stringBuffer.append("AND( ((sweep.movementIdCr IN (" + crQuery + ")) OR (sweep.movementIdDr IN (" + drQuery + "))) OR (sweep.movementIdCr is null and sweep.movementIdDr is null)) ");

			// BookCode and Account Type
			// case when both are having some value
			if (!bookCode.equals("")
					&& (!acctType.equals(SwtConstants.AUTHORISE_STATUS))) {
				bookQuery = new StringBuffer(
						"select distinct acc.id.accountId from AcctMaintenance acc,Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId=sweep.entityIdCr) or (acc.id.accountId=sweep.accountIdDr and  acc.id.entityId=sweep.entityIdDr)) and (acc.id.hostId='")
						.append(hostId).append("') and (sweep.entityIdCr='")
						.append(entityId).append("' or sweep.entityIdDr='")
						.append(entityId).append("') and acc.sweepbookcode='")
						.append(bookCode).append("'and acc.accttype='").append(
								acctType).append("'");
				bookList = (List<Sweep>) getHibernateTemplate().find(bookQuery.toString());
				if (bookList.size() == 0) {
					stringBuffer
							.append(" and (sweep.accountIdDr IN('') and sweep.accountIdCr IN (''))");
				}
				for (int i = 0; i < bookList.size(); i++) {
					if (i == 0) {
						stringBuffer.append("and (sweep.accountIdDr IN('");
					}
					if (i == (bookList.size() - 1)) {
						stringBuffer.append(bookList.get(i) + "'");
						stringBuffer.append(") or sweep.accountIdCr IN ('");
					} else {
						stringBuffer.append(bookList.get(i) + "','");
					}
				}
				for (int i = 0; i < bookList.size(); i++) {
					if (i == (bookList.size() - 1)) {
						stringBuffer.append(bookList.get(i) + "'");
						stringBuffer.append("))");
					} else {
						stringBuffer.append(bookList.get(i) + "','");
					}
				}
			} else if (!bookCode.equals("")
					&& (acctType.equals(SwtConstants.AUTHORISE_STATUS))) {
				bookQuery = new StringBuffer(
						"select distinct acc.id.accountId from AcctMaintenance acc,Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId=sweep.entityIdCr) or (acc.id.accountId=sweep.accountIdDr and  acc.id.entityId=sweep.entityIdDr)) and (acc.id.hostId='")
						.append(hostId).append("') and (sweep.entityIdCr='")
						.append(entityId).append("' or sweep.entityIdDr='")
						.append(entityId).append("') and acc.sweepbookcode='")
						.append(bookCode).append("'");
				bookList = (List<Sweep>) getHibernateTemplate().find(bookQuery.toString());

				if (bookList.size() == 0) {
					stringBuffer
							.append(" and (sweep.accountIdDr IN('') and sweep.accountIdCr IN (''))");
				}

				for (int i = 0; i < bookList.size(); i++) {
					if (i == 0) {
						stringBuffer.append("and (sweep.accountIdDr IN('");
					}

					if (i == (bookList.size() - 1)) {
						stringBuffer.append(bookList.get(i) + "'");
						stringBuffer.append(") or sweep.accountIdCr IN ('");
					} else {
						stringBuffer.append(bookList.get(i) + "','");
					}
				}

				for (int i = 0; i < bookList.size(); i++) {
					if (i == (bookList.size() - 1)) {
						stringBuffer.append(bookList.get(i) + "'");
						stringBuffer.append("))");
					} else {
						stringBuffer.append(bookList.get(i) + "','");
					}
				}
			} else if (bookCode.equals("")
					&& (!acctType.equals(SwtConstants.AUTHORISE_STATUS))) {
				bookQuery = new StringBuffer(
						"select distinct acc.id.accountId from AcctMaintenance acc,Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId=sweep.entityIdCr) or (acc.id.accountId=sweep.accountIdDr and  acc.id.entityId=sweep.entityIdDr)) and (acc.id.hostId='")
						.append(hostId).append("') and (sweep.entityIdCr='")
						.append(entityId).append("' or sweep.entityIdDr='")
						.append(entityId).append("') and acc.accttype='")
						.append(acctType).append("'");
				bookList = (List<Sweep>) getHibernateTemplate().find(bookQuery.toString());

				if (bookList.size() == 0) {
					stringBuffer
							.append(" and (sweep.accountIdDr IN('') and sweep.accountIdCr IN (''))");
				}
				for (int i = 0; i < bookList.size(); i++) {
					if (i == 0) {
						stringBuffer.append("and (sweep.accountIdDr IN('");
					}

					if (i == (bookList.size() - 1)) {
						stringBuffer.append(bookList.get(i) + "'");
						stringBuffer.append(") or sweep.accountIdCr IN ('");
					} else {
						stringBuffer.append(bookList.get(i) + "','");
					}
				}
				for (int i = 0; i < bookList.size(); i++) {
					if (i == (bookList.size() - 1)) {
						stringBuffer.append(bookList.get(i) + "'");
						stringBuffer.append("))");
					} else {
						stringBuffer.append(bookList.get(i) + "','");
					}
				}
			}
			over = amountOver.doubleValue();
			under = amountUnder.doubleValue();
			if (over == 0.0) {
				if (under == 0.0) {
					stringBuffer = calculatestatus(stringBuffer, status, type,
							postCutOff, hostId, entityId);
				} else {
					stringBuffer = calculateamount(stringBuffer, status, type,
							over, under);
				}
			} else {
				if (under == 0.0) {
					stringBuffer = calculateamount1(stringBuffer, status, type,
							over);
				} else {
					stringBuffer = calculateamount(stringBuffer, status, type,
							over, under);
				}
			}
			// Get the Message Format details
			if (!message.equals("")) {
				if (status.equals(SwtConstants.AUTHORISE_STATUS)) {
					crMsgQuery = new StringBuffer(
							"select distinct sweep.id.sweepId from Sweep sweep, AcctMaintenance acc where sweep.accountIdCr=acc.id.accountId and sweep.entityIdCr=acc.id.entityId and sweep.movementIdCr is not null and sweep.id.hostId='")
							.append(hostId).append("' and (sweep.entityIdCr='")
							.append(entityId).append("' or sweep.entityIdDr='")
							.append(entityId).append(
									"') and (acc.acctNewCrInternal='").append(
									message).append(
									"' or acc.acctNewCrExternal='").append(
									message).append("')");

					drMsgQuery = new StringBuffer(
							"select distinct sweep.id.sweepId from Sweep sweep,AcctMaintenance acc where sweep.accountIdDr=acc.id.accountId and sweep.entityIdDr=acc.id.entityId and sweep.movementIdDr is not null and sweep.id.hostId='")
							.append(hostId).append("' and (sweep.entityIdCr='")
							.append(entityId).append("' or sweep.entityIdDr='")
							.append(entityId).append(
									"') and (acc.acctNewDrInternal='").append(
									message).append(
									"' or acc.acctNewDrExternal='").append(
									message).append("')");
				}
				/**
				 * Start: Code modified by sandeepkumar on ******** for Mantis
				 * 1819 - Sweep search result screen displays error while open
				 * from sweep search screen
				 * 
				 */
				else if (status.equals(SwtConstants.SWEEP_STATUS_STP)
						|| status.equals(SwtConstants.SWEEP_STATUS_SUBMIT)
						|| status.equals(SwtConstants.SWEEP_STATUS_NEW)) {
					/**
					 * End: Code modified by sandeepkumar on ******** for Mantis
					 * 1819 - Sweep search result screen displays error while
					 * open from sweep search screen
					 * 
					 */
					crMsgQuery = new StringBuffer(
							"select distinct sweep.id.sweepId from Sweep sweep,AcctMaintenance acc where sweep.accountIdCr=acc.id.accountId and sweep.entityIdCr=acc.id.entityId and sweep.movementIdCr is not null and sweep.id.hostId='")
							.append(hostId).append("' and (sweep.entityIdCr='")
							.append(entityId).append("' or sweep.entityIdDr='")
							.append(entityId).append(
									"') and (acc.acctNewCrInternal='").append(
									message).append(
									"' or acc.acctNewCrExternal='").append(
									message).append("')");

					drMsgQuery = new StringBuffer(
							"select distinct sweep.id.sweepId from Sweep sweep,AcctMaintenance acc where sweep.accountIdDr=acc.id.accountId and sweep.entityIdDr=acc.id.entityId and sweep.movementIdDr is not null and sweep.id.hostId='")
							.append(hostId).append("' and (sweep.entityIdCr='")
							.append(entityId).append("' or sweep.entityIdDr='")
							.append(entityId).append(
									"') and (acc.acctNewDrInternal='").append(
									message).append(
									"' or acc.acctNewDrExternal='").append(
									message).append("')");
				} else if (status.equals(SwtConstants.BOOK)) {
					crMsgQuery = new StringBuffer(
							"select distinct sweep.id.sweepId from Sweep sweep,AcctMaintenance acc where sweep.accountIdCr=acc.id.accountId and sweep.entityIdCr=acc.id.entityId and sweep.movementIdCr is not null and sweep.id.hostId='")
							.append(hostId).append("' and (sweep.entityIdCr='")
							.append(entityId).append("' or sweep.entityIdDr='")
							.append(entityId).append("')");

					drMsgQuery = new StringBuffer(
							"select distinct sweep.id.sweepId from Sweep sweep,AcctMaintenance acc where sweep.accountIdDr=acc.id.accountId and sweep.entityIdDr=acc.id.entityId and sweep.movementIdDr is not null and sweep.id.hostId='")
							.append(hostId).append("' and (sweep.entityIdCr='")
							.append(entityId).append("' or sweep.entityIdDr='")
							.append(entityId).append("')");
				}
				
				stringBuffer.append(" AND (sweep.id.sweepId IN (" + crMsgQuery+ ") or  sweep.id.sweepId IN (" + drMsgQuery + "))");
				
			}
			
			
			// initialize the noOfVar value as zero
			noOfVar = 0;
			// Getting the from date
			inputValue = new ArrayList<Date>();
			// get the today date
			todayDate = SwtUtil.getSystemDatewithoutTime();
			// getting the from date
			fromDateAsDate = SwtUtil.parseDateGeneral(fromDate);
			// get the time form form date
			fromDateAsDate = SwtUtil.removeTime(fromDateAsDate);
			// checks to date not equals to empty
			if (!toDate.equals("")) {
				todateAsDate = SwtUtil.parseDateGeneral(toDate);
				todateAsDate = SwtUtil.removeTime(todateAsDate);
			}
			// checks from date not equal to empty
			int paramCount =0;
			if (!fromDate.equals("")) {
				if (!toDate.equals("")) {
					if (format
							.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_DELIMITED)) {
						stringBuffer.append(" and sweep.valueDate >= ?"+noOfVar+" ");
						inputValue.add(noOfVar, fromDateAsDate);
						++noOfVar;

						stringBuffer.append(" and sweep.valueDate <= ?"+noOfVar+" ");
						inputValue.add(noOfVar, todateAsDate);
						++noOfVar;
					} else {
						stringBuffer.append(" and sweep.valueDate >= ?"+noOfVar+" ");
						inputValue.add(noOfVar, fromDateAsDate);
						++noOfVar;

						stringBuffer.append(" and sweep.valueDate <= ?"+noOfVar+" ");
						inputValue.add(noOfVar, todateAsDate);
						++noOfVar;
					}
				} else {
					if (format
							.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_DELIMITED)) {
						stringBuffer.append(" and sweep.valueDate >= ?"+noOfVar+" ");
						inputValue.add(noOfVar, fromDateAsDate);
						++noOfVar;
					} else {
						stringBuffer.append(" and sweep.valueDate >= ?"+noOfVar+" ");
						inputValue.add(noOfVar, fromDateAsDate);
						++noOfVar;
					}
				}
			} else if (!toDate.equals("")) {
				if (format.equalsIgnoreCase(SwtConstants.FORMAT_TYPE_DELIMITED)) {
					stringBuffer.append(" and sweep.valueDate <= ?"+noOfVar+" ");
					inputValue.add(noOfVar, todateAsDate);
					++noOfVar;
				} else {
					stringBuffer.append(" and sweep.valueDate <= ?"+noOfVar+" ");
					inputValue.add(noOfVar, todateAsDate);
					++noOfVar;
				}
			} else {
				String date = "sysdate";
				stringBuffer.append(" and sweep.valueDate >= ?"+noOfVar+" ");
				inputValue.add(noOfVar, todayDate);
				++noOfVar;
			}
			// Get the Positon Cut off details
			if (postCutOff.equals(SwtConstants.YES)) {
				if (postCutOff != null) {
					arrForSwepId = new ArrayList<Object>();
					if ((postCutOff.equals("Y"))
							&& (status
									.equals(SwtConstants.SWEEP_STATUS_AUTHORIZE))) {
						posQuery = new StringBuffer(
								"select acc.id.entityId, acc.cutoff,to_char(sweep.updateDate,'HH24:MI'),sweep.id.sweepId from AcctMaintenance acc,")
								.append(
										"Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId= sweep.entityIdCr)")
								.append(
										"or (acc.id.accountId=sweep.accountIdDr and acc.id.entityId= sweep.entityIdDr))")
								.append("and  sweep.id.hostId = '").append(
										hostId).append(
										"' and (sweep.entityIdCr='").append(
										entityId).append(
										"' or sweep.entityIdDr='").append(
										entityId).append(
										"') order by sweep.id.sweepId");
					}
					if ((postCutOff.equals(SwtConstants.YES))
							&& (status.equals(SwtConstants.SWEEP_STATUS_STP))) {
						posQuery = new StringBuffer(
								"select acc.id.entityId, acc.cutoff,to_char(sweep.submitDateTime,'HH24:MI'),sweep.id.sweepId from AcctMaintenance acc,")
								.append(
										"Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId= sweep.entityIdCr)")
								.append(
										"or (acc.id.accountId=sweep.accountIdDr and acc.id.entityId= sweep.entityIdDr))")
								.append("and  sweep.id.hostId = '")
								.append(hostId)
								.append("' and (sweep.entityIdCr='")
								.append(entityId)
								.append("' or sweep.entityIdDr='")
								.append(entityId)
								.append("') and sweep.sweepStatus='")
								.append(SwtConstants.SWEEP_STATUS_SUBMIT)
								.append(
										"' and sweep.submitDateTime is not null order by sweep.id.sweepId");
					}
					if ((postCutOff.equals(SwtConstants.YES))
							&& (status.equals(SwtConstants.SWEEP_STATUS_SUBMIT))) {
						posQuery = new StringBuffer(
								"select acc.id.entityId, acc.cutoff,to_char(sweep.authDateTime,'HH24:MI'),sweep.id.sweepId from AcctMaintenance acc,")
								.append(
										"Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId= sweep.entityIdCr)")
								.append(
										"or (acc.id.accountId=sweep.accountIdDr and acc.id.entityId= sweep.entityIdDr))")
								.append("and  sweep.id.hostId = '")
								.append(hostId)
								.append("' and (sweep.entityIdCr='")
								.append(entityId)
								.append("' or sweep.entityIdDr='")
								.append(entityId)
								.append("') and sweep.sweepStatus='")
								.append(SwtConstants.SWEEP_STATUS_AUTHORIZE)
								.append(
										"' and sweep.authDateTime is not null order by sweep.id.sweepId");
					}
					if ((postCutOff.equals(SwtConstants.YES))
							&& (status.equals(SwtConstants.BOOK))) {
						posQuery = new StringBuffer(
								"select acc.id.entityId, acc.cutoff,to_char(sweep.cancelDateTime,'HH24:MI'),sweep.id.sweepId from AcctMaintenance acc,")
								.append(
										"Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId= sweep.entityIdCr)")
								.append(
										"or (acc.id.accountId=sweep.accountIdDr and acc.id.entityId= sweep.entityIdDr))")
								.append("and  sweep.id.hostId = '")
								.append(hostId)
								.append("' and (sweep.entityIdCr='")
								.append(entityId)
								.append("' or sweep.entityIdDr='")
								.append(entityId)
								.append("') and sweep.sweepStatus='")
								.append(SwtConstants.SWEEP_STATUS_CANCEL)
								.append(
										"' and sweep.cancelDateTime is not null order by sweep.id.sweepId");
					}
					if ((postCutOff.equals(SwtConstants.YES))
							&& (status.equals(SwtConstants.SWEEP_STATUS_NEW))) {
						posQuery = new StringBuffer(
								"select acc.id.entityId, acc.cutoff,to_char(sweep.inputDateTime,'HH24:MI'),sweep.id.sweepId from AcctMaintenance acc,")
								.append(
										"Sweep sweep where ((acc.id.accountId=sweep.accountIdCr and acc.id.entityId= sweep.entityIdCr)")
								.append(
										"or (acc.id.accountId=sweep.accountIdDr and acc.id.entityId= sweep.entityIdDr))")
								.append("and  sweep.id.hostId = '").append(
										hostId).append(
										"' and (sweep.entityIdCr='").append(
										entityId).append(
										"' or sweep.entityIdDr='").append(
										entityId).append(
										"') order by sweep.id.sweepId");
					}
					qryList = (List<Object[]>) getHibernateTemplate().find(posQuery.toString());
					// instance for entity list
					entityList = new ArrayList<Object>();
					// instance for cuttofflist
					cutOffList = new ArrayList<Object>();
					// instance for sweepidslist
					sweepIdsList = new ArrayList<Object>();
					// instance for time list
					timeList = new ArrayList<Object>();
					// iterate the query list
					iterator = qryList.iterator();
					while (iterator.hasNext()) {
						row = iterator.next();
						entityList.add(row[0]);
						cutOffList.add(row[1]);
						timeList.add(row[2]);
						sweepIdsList.add(row[3]);
					}
					// instance for final entity list
					finalEntityList = new ArrayList<Object>();
					// instance for final cut off list
					finalCuttOffList = new ArrayList<Object>();
					// instance for final time list
					finalTimeList = new ArrayList<Object>();
					// instance for final sweep id list
					finalSweepIdList = new ArrayList<Object>();
					for (int i = 0; i < cutOffList.size(); i++) {
						// get the strcuttoff
						strCutOff = cutOffList.get(i).toString();
						// check for the index of :
						index = strCutOff.indexOf(":");
						// get the hout form strcutoff
						hour = strCutOff.substring(0, index);
						minute = strCutOff.substring(index + 1, strCutOff
								.length());
						// instance for dateGregorian
						dateGregorian = new GregorianCalendar();
						// set the Calendar hour of day
						dateGregorian.set(Calendar.HOUR_OF_DAY, Integer
								.parseInt(hour));
						// set the calender minutes
						dateGregorian.set(Calendar.MINUTE, Integer
								.parseInt(minute));
						i++;
						// get the strcutoff
						strCutOff = cutOffList.get(i).toString();
						// check for the index of :
						index = strCutOff.indexOf(":");
						// get the hout form strcutoff
						hour = strCutOff.substring(0, index);
						minute = strCutOff.substring(index + 1, strCutOff
								.length());
						// instance for dateGregorian
						dateGre = new GregorianCalendar();
						// set the Calendar hour of day
						dateGre.set(Calendar.HOUR_OF_DAY, Integer
								.parseInt(hour));
						// set the calender minutes
						dateGre.set(Calendar.MINUTE, Integer.parseInt(minute));
						// checks the dateGregorian date
						if (dateGregorian.after(dateGre)) {
							// adding the records to
							// finalEntityList,finalCuttOffList,finalTimeList,finalSweepIdList
							finalEntityList.add(entityList.get(i));
							finalCuttOffList.add(cutOffList.get(i));
							finalTimeList.add(timeList.get(i));
							finalSweepIdList.add(sweepIdsList.get(i));
						} else {
							// adding the records to
							// finalEntityList,finalCuttOffList,finalTimeList,finalSweepIdList
							finalEntityList.add(entityList.get(i - 1));
							finalCuttOffList.add(cutOffList.get(i - 1));
							finalTimeList.add(timeList.get(i - 1));
							finalSweepIdList.add(sweepIdsList.get(i - 1));
						}
					}
					// instance for offset list
					offSetList = new ArrayList<Object[]>();
					
					// Remove duplicate entities from the list
					HashSet<Object> dedupEntities = new HashSet<Object>(finalEntityList);
					finalEntityList = new ArrayList<Object>();
					for(Object entity:dedupEntities.toArray()){
						finalEntityList.add(entity);
					}
					
					for (int i = 0; i < finalEntityList.size(); i++) {
						if (i != (finalEntityList.size() - 1)) {
							entities += (finalEntityList.get(i) + "','");
						} else {
							entities += finalEntityList.get(i);
						}
					}
					// Get the off set list
					posQuery = new StringBuffer(
							"select e from Entity e where e.id.entityId in ('")
							.append(entities).append("')");
					List<Entity> entityListTmp = (List<Entity>) getHibernateTemplate().find(
							posQuery.toString());
					
					for (int i = 0; i < entityListTmp.size(); i++) {
						offSetList.add(new Object[] {entityListTmp.get(i).getEntServerTimeOffSet(), entityListTmp.get(i).getId().getEntityId()});
					}
					
					// iterate the itroffset detials
					itrOffSet = offSetList.iterator();
					for (int i = 0; i < finalEntityList.size(); i++) {
						itrOffSet = offSetList.iterator();
						while (itrOffSet.hasNext()) {
							result = itrOffSet.next();
							// checks the final entity list value
							if (finalEntityList.get(i).equals(result[1])) {
								// Get the final time list
								strFinalTime = finalTimeList.get(i).toString();
								// get the str result
								strResult = result[0].toString();
								// get the str final index of :
								strFinalIndex = strFinalTime.indexOf(":");
								// get the final hour
								strFinalHour = Integer.parseInt(strFinalTime
										.substring(0, strFinalIndex));
								// get the final minutes
								strFinalMin = Integer.parseInt(strFinalTime
										.substring(strFinalIndex + 1));
								// get the result index
								strResultIndex = strResult.indexOf("-");
								// checks the result index greater than or
								// equals to zero
								if (strResultIndex >= 0) {
									strResult = strResult
											.substring(strResultIndex + 1);
								} else if (strResult.indexOf("+") >= 0) {
									strResult = strResult.substring(strResult
											.indexOf("+") + 1);
								}
								// get the strresindex of :
								strResIndex = strResult.indexOf(":");
								// get the result hour
								strResultHour = Integer.parseInt(strResult
										.substring(0, strResIndex));
								// get the result minutes
								strResultMin = Integer.parseInt(strResult
										.substring(strResIndex + 1));
								offSetTime = (strResultHour * 60)
										+ strResultMin;
								cutOffTime = (strFinalHour * 60) + strFinalMin;
								// initalize the efftime as zero
								effTime = 0;
								// checks result index greater than or equal ot
								// zero
								if (strResultIndex >= 0) {
									effTime = cutOffTime - offSetTime;
								} else {
									effTime = cutOffTime + offSetTime;
								}
								// calculate the efftime
								effTimeHour = effTime / 60;
								effTimeMin = effTime % 60;
								effTimeHour = (effTimeHour + (effTimeMin / 60)) % 24;
								// caluculate the final time in minutes
								finalTimeInMin = (effTimeHour * 60)
										+ effTimeMin;
								// get the temp cutt off
								tempCutOff = finalCuttOffList.get(i).toString();
								// get the cut off index of :
								strCutOffIndex = tempCutOff.indexOf(":");
								// get the cut of hour time
								strCutOffHour = Integer.parseInt(tempCutOff
										.substring(0, strCutOffIndex));
								// get the cut off minutes
								strCutOffMin = Integer.parseInt(tempCutOff
										.substring(strCutOffIndex + 1));
								tempCutOffInMin = (strCutOffHour * 60)
										+ strCutOffMin;
								// checks the temp cutt off in minutes
								if (tempCutOffInMin < finalTimeInMin) {
									arrForSwepId.add(finalSweepIdList.get(i));
								}
							}
						}
					}
					for (int i = 0; i < arrForSwepId.size(); i++) {
						if (i != (arrForSwepId.size() - 1)) {
							postSweepList += (arrForSwepId.get(i) + "','");
						} else {
							postSweepList += arrForSwepId.get(i);
						}
					}
					// checks the postSweepList is equals to zero
					if (postSweepList.trim().equals("")) {
						stringBuffer.append("and sweep.id.sweepId in ('"
								+ postSweepList + "')");
					}
				}
			}
			// Sweep status, Sweep Type and amount
			strQuery = stringBuffer.toString();
			
			
			
			
			
			if(SwtUtil.isEmptyOrNull(archiveid)) {
				sweepList = (List<Object>) getHibernateTemplate().find(strQuery,
				inputValue.toArray());
			}else {
				Session session  = null;
				Connection conn = null;
				ResultSet resultSet = null;
				PreparedStatement statement = null;
				List<Object> pSweepList = new ArrayList<>();
				try {
					String  query = "select * "+replaceFieldsInHQLWithArchiveId(strQuery, hostId, archiveid);
				 session = getHibernateTemplate().getSessionFactory().openSession();
					conn = SwtUtil.connection(session);
					statement = conn.prepareStatement(query);
					for (int i = 0; i < inputValue.size(); i++) {
						statement.setDate(i+1,SwtUtil.truncateDateTime(inputValue.get(i)));
					}
					
					statement.execute();
					resultSet = statement.getResultSet();
					
					if (resultSet != null) {
						while (resultSet.next()) {
							 Sweep pSweep = new Sweep();
							 pSweep.getId().setHostId(resultSet.getString("HOST_ID"));
							 pSweep.getId().setSweepId(resultSet.getLong("SWEEP_ID"));
							 pSweep.setSweepGroupId(resultSet.getString("SWEEP_GROUP_ID"));
							 pSweep.setCurrencyCode(resultSet.getString("CURRENCY_CODE"));
							 pSweep.setMovementIdCr(resultSet.getString("MOVEMENT_ID_CR"));
							 pSweep.setMovementIdDr(resultSet.getString("MOVEMENT_ID_DR"));
							 pSweep.setOriginalSweepAmt(resultSet.getObject("ORIGINAL_SWEEP_AMT") != null ? resultSet.getDouble("ORIGINAL_SWEEP_AMT") : null);
							 pSweep.setSubmitSweepAmt(resultSet.getObject("SUBMIT_SWEEP_AMT") != null ? resultSet.getDouble("SUBMIT_SWEEP_AMT") : null);
							 pSweep.setAuthorizeSweepAmt(resultSet.getObject("AUTHORIZE_SWEEP_AMOUNT") != null ? resultSet.getDouble("AUTHORIZE_SWEEP_AMOUNT") : null);
							 pSweep.setSweepType(resultSet.getString("SWEEP_TYPE"));
							 pSweep.setSweepStatus(resultSet.getString("SWEEP_STATUS"));
							 pSweep.setInputDateTime(resultSet.getDate("INPUT_DATE_TIME"));
							 pSweep.setAuthDateTime(resultSet.getDate("AUTHORIZED_DATE_TIME"));
							 pSweep.setInputUser(resultSet.getString("INPUT_USER"));
							 pSweep.setSubmitUser(resultSet.getString("SUBMIT_USER"));
							 pSweep.setAuthorizedUser(resultSet.getString("AUTHORIZED_USER"));
							 pSweep.setCancelUser(resultSet.getString("CANCEL_USER"));
							 pSweep.setAccountIdCr(resultSet.getObject("ACCOUNT_ID_CR") != null ? resultSet.getString("ACCOUNT_ID_CR") : null);
							 pSweep.setAccountIdDr(resultSet.getObject("ACCOUNT_ID_DR") != null ? resultSet.getString("ACCOUNT_ID_DR") : null);
							 pSweep.setAlignAccountId(resultSet.getObject("ALIGN_ACCOUNT_ID") != null ? resultSet.getString("ALIGN_ACCOUNT_ID") : null);
							 pSweep.setValueDate(resultSet.getDate("VALUE_DATE"));
							 pSweep.setUpdateDate(resultSet.getDate("UPDATE_DATE"));
							 pSweep.setUpdateUser(resultSet.getString("UPDATE_USER"));
							 pSweep.setSubmitDateTime(resultSet.getDate("SUBMIT_DATE_TIME"));
							 pSweep.setCancelDateTime(resultSet.getDate("CANCEL_DATE_TIME"));
							 pSweep.setEntityIdCr(resultSet.getObject("ENTITY_ID_CR") != null ? resultSet.getString("ENTITY_ID_CR") : null);
							 pSweep.setEntityIdDr(resultSet.getObject("ENTITY_ID_DR") != null ? resultSet.getString("ENTITY_ID_DR") : null);
							 pSweep.setSettleMethodCR(resultSet.getString("SETTL_METHOD_CR"));
							 pSweep.setSettleMethodDR(resultSet.getString("SETTL_METHOD_DR"));
							 pSweep.setBookCodeCR(resultSet.getString("BOOKCODE_CR"));
							 pSweep.setBookCodeDR(resultSet.getString("BOOKCODE_DR"));
							 pSweep.setAdditionalReference(resultSet.getString("ADDITIONAL_REFERENCE"));
							 pSweep.setTargetBalance(resultSet.getObject("TARGET_BALANCE") != null ? resultSet.getDouble("TARGET_BALANCE") : null);
							 pSweep.setTargetBalanceType(resultSet.getString("TARGET_BALANCE_TYPE"));
							 pSweep.setMinAmount(resultSet.getObject("MIN_AMOUNT") != null ? resultSet.getDouble("MIN_AMOUNT") : null);
							 pSweep.setAccountIdOrigin(resultSet.getObject("ACCOUNT_ID_ORIGIN") != null ? resultSet.getString("ACCOUNT_ID_ORIGIN") : null);
							 pSweep.setEntityIdOrigin(resultSet.getObject("ENTITY_ID_ORIGIN") != null ? resultSet.getString("ENTITY_ID_ORIGIN") : null);
							 pSweep.setSweepFromBalanceTypeCr(resultSet.getString("SWEEP_FROM_BALANCE_TYPE_CR"));
							 pSweep.setSweepFromBalanceTypeDr(resultSet.getString("SWEEP_FROM_BALANCE_TYPE_DR"));
							 pSweep.setTargetBalanceTypId(resultSet.getString("TARGET_BALANCE_TYPE_ID"));
							 
							    
						     pSweepList.add(pSweep);
						}
					}
					sweepList = pSweepList;
					
					if(sweepList.size() > 500) {
						
						Set<String> entityIdSet = new HashSet<>();
						for (Object pSweep : pSweepList) {
							Sweep sweep = (Sweep)  pSweep;
						    entityIdSet.add(sweep.getEntityIdCr());
						    entityIdSet.add(sweep.getEntityIdDr());
						}

						List<String> distinctEntityIds = new ArrayList<>(entityIdSet);
						
						
						String accountAllForEntity = "from AcctMaintenance acc where acc.id.hostId = :hostId and acc.id.entityId in (:entityIds)";
						java.util.List<AcctMaintenance> AccList = (List<AcctMaintenance>) getHibernateTemplate().findByNamedParam(
						    accountAllForEntity,
						    new String[] { "hostId", "entityIds" },
						    new Object[] { hostId, distinctEntityIds }
						);

						

						for (int i = 0; i < pSweepList.size(); i++) {
							Sweep pSweep = (Sweep) pSweepList.get(i);
							
							 AcctMaintenance accountCr = AccList.stream()
								        .filter(acc -> acc.getId().getHostId().equals(pSweep.getId().getHostId())
								                && acc.getId().getEntityId().equals(pSweep.getEntityIdCr())
								                && acc.getId().getAccountId().equals(pSweep.getAccountIdCr()))
								        .findFirst().orElse(null);
								    AcctMaintenance accountDr = AccList.stream()
								        .filter(acc -> acc.getId().getHostId().equals(pSweep.getId().getHostId())
								                && acc.getId().getEntityId().equals(pSweep.getEntityIdDr())
								                && acc.getId().getAccountId().equals(pSweep.getAccountIdDr())
								                )
								        .findFirst().orElse(null);
								    pSweep.setAccountCr(accountCr);
								    pSweep.setAccountDr(accountDr);
						}
						
					}else {
						
						for (int i = 0; i < pSweepList.size(); i++) {
							Sweep pSweep = (Sweep) pSweepList.get(i);
							
				
						String accountCutoffTime = "from AcctMaintenance acc where acc.id.hostId=?0 and acc.id.entityId=?1 and acc.id.accountId=?2 ";
						java.util.List AccListCr = getHibernateTemplate().find(
								accountCutoffTime,
								new Object[] { pSweep.getId().getHostId(), pSweep.getEntityIdCr(), pSweep.getAccountIdCr() });
						java.util.List AccListDr = getHibernateTemplate().find(
								accountCutoffTime,
								new Object[] { pSweep.getId().getHostId(), pSweep.getEntityIdDr(), pSweep.getAccountIdDr() });
					
						AcctMaintenance accountCr = (AcctMaintenance) AccListCr.get(0);
						AcctMaintenance accountDr = (AcctMaintenance) AccListDr.get(0);
					    
					     pSweep.setAccountCr(accountCr);
					     pSweep.setAccountDr(accountDr);
//							
//					     
						}
						
					}
					
				}catch(Exception e)	{
					e.printStackTrace();
				}finally {
					Object[] exceptions = JDBCCloser.close(resultSet,statement, conn, session);
				}
					
			}
			
			
			
				

			

			
		} catch (Exception exp) {
			exp.printStackTrace();
			log.error(this.getClass().getName()
					+ " - Exception Catched in [fetchdetails] method : - "
					+ exp.getMessage());

			throw SwtErrorHandler.getInstance().handleException(exp,
					"fetchdetails", SweepSearchDAOHibernate.class);
		} finally {
			// nullify Objects
			stringBuffer = null;
			crQuery = null;
			drQuery = null;
			bookQuery = null;
			crMsgQuery = null;
			drMsgQuery = null;
			inputValue = null;
			todayDate = null;
			fromDateAsDate = null;
			todateAsDate = null;
			arrForSwepId = null;
			posQuery = null;
			entityList = null;
			cutOffList = null;
			sweepIdsList = null;
			timeList = null;
			row = null;
			iterator = null;
			finalEntityList = null;
			finalCuttOffList = null;
			finalTimeList = null;
			finalSweepIdList = null;
			strCutOff = null;
			hour = null;
			minute = null;
			dateGregorian = null;
			dateGre = null;
			entities = null;
			result = null;
			itrOffSet = null;
			strFinalTime = null;
			strResult = null;
			tempCutOff = null;
			postSweepList = null;
			strQuery = null;
			log.debug(this.getClass().getName() + "- [fetchdetails] - Exiting");
		}
		return sweepList;
	}

	// Function for status only
	private StringBuffer calculatestatus(StringBuffer sb, String st, String ty,
			String postcutoff, String hostid, String entityid) {
		String status = "";
		String type = "";
		if (!st.equals("A")) {
			if (st.equals("N")) {
				status = SwtConstants.SWEEP_STATUS_NEW;
				sb.append(" and (sweep.sweepStatus='" + status
						+ "' or sweep.sweepStatus='S')");
			} else {
				if (st.equals("S")) {
					status = SwtConstants.SWEEP_STATUS_SUBMIT;
				} else if (st.equals("B")) {
					status = SwtConstants.SWEEP_STATUS_CANCEL;
				} else if (st.equals("U")) {
					status = SwtConstants.SWEEP_STATUS_AUTHORIZE;
				}

				sb.append(" and sweep.sweepStatus='" + status + "'");
			}
		}

		if (!ty.equals("A")) {
			if (ty.equals("U")) {
				type = SwtConstants.SWEEP_TYPE_AUTO;
			} else if (ty.equals("M")) {
				type = SwtConstants.SWEEP_TYPE_MANUAL;
			}

			sb.append(" and sweep.sweepType='" + type + "'");
		}

		return sb;
	}

	/**
	 * @param StringBuffer
	 * @param status
	 * @param type
	 * @param amountover
	 * @param amountunder
	 *            returns StringBuffer Method used to form sub query to handle
	 *            searched amount
	 */

	private StringBuffer calculateamount(StringBuffer sb, String status,
			String type, double amountover, double amountunder) {
		log.debug(this.getClass().getName() + " -[calculateamount]- Entering");
		if (status.equals("N")) {
			if (type.equals("U")) {
				sb
						.append(" and (sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_NEW
								+ "' or sweep.sweepStatus='S') and sweep.sweepType='"
								+ SwtConstants.SWEEP_TYPE_AUTO
								+ "'and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}

			if (type.equals("M")) {
				sb
						.append(" and (sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_NEW
								+ "' or sweep.sweepStatus='S') and sweep.sweepType='"
								+ SwtConstants.SWEEP_TYPE_MANUAL
								+ "'and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}

			if (type.equals("A")) {
				sb
						.append(" and (sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_NEW
								+ "' or sweep.sweepStatus='S') and sweep.originalSweepAmt>='"
								+ amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}
		} else if (status.equals("S")) {
			if (type.equals("U")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_SUBMIT
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "' and ((sweep.submitSweepAmt>='" + amountover
						+ "' and sweep.submitSweepAmt<='" + amountunder
						+ "' )or (sweep.originalSweepAmt>='" + amountover
						+ "' and  sweep.originalSweepAmt<='" + amountunder
						+ "'))");
			}

			if (type.equals("M")) {
				sb.append(" and  ((sweep.submitSweepAmt>='" + amountover
						+ "' and sweep.submitSweepAmt<='" + amountunder
						+ "' )or (sweep.originalSweepAmt>='" + amountover
						+ "' and  sweep.originalSweepAmt<='" + amountunder
						+ "'))");
			}

			if (type.equals("A")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_SUBMIT
						+ "'and ((sweep.submitSweepAmt>='" + amountover
						+ "'and  sweep.submitSweepAmt<='" + amountunder
						+ "' )or (sweep.originalSweepAmt>='" + amountover
						+ "' and sweep.originalSweepAmt<='" + amountunder
						+ "'))");
			}
		} else if (status.equals("U")) {

			if (type.equals("U")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_AUTHORIZE
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "' and ((sweep.authorizeSweepAmt>='" + amountover
						+ "' and  sweep.authorizeSweepAmt<='" + amountunder
						+ "' ) or (sweep.originalSweepAmt>='" + amountover
						+ "' and  sweep.originalSweepAmt<='" + amountunder
						+ "'))");
			}

			if (type.equals("M")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_AUTHORIZE
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_MANUAL
						+ "'and ((sweep.authorizeSweepAmt>='" + amountover
						+ "'and sweep.authorizeSweepAmt<='" + amountunder
						+ "' )or (sweep.originalSweepAmt>='" + amountover
						+ "' and sweep.originalSweepAmt<='" + amountunder
						+ "'))");
			}

			if (type.equals("A")) {

				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_AUTHORIZE
						+ "'and ((sweep.authorizeSweepAmt>='" + amountover
						+ "' and sweep.authorizeSweepAmt<='" + amountunder
						+ "' ) or  (sweep.originalSweepAmt>='" + amountover
						+ "' and sweep.originalSweepAmt<='" + amountunder
						+ "'))");

			}
		} else if (status.equals("B")) {

			if (type.equals("U")) {
				sb
						.append(" and sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_CANCEL
								+ "' and sweep.sweepType='"
								+ SwtConstants.SWEEP_TYPE_AUTO
								+ "'and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}

			if (type.equals("M")) {
				sb
						.append(" and sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_CANCEL
								+ "' and sweep.sweepType='"
								+ SwtConstants.SWEEP_TYPE_MANUAL
								+ "'and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}

			if (type.equals("A")) {
				sb
						.append(" and sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_CANCEL
								+ "' and sweep.originalSweepAmt>='"
								+ amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}
		} else {

			if (type.equals("U")) {
				sb
						.append(" and sweep.sweepType='"
								+ SwtConstants.SWEEP_TYPE_AUTO
								+ "'and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}

			if (type.equals("M")) {
				sb
						.append(" and sweep.sweepType='"
								+ SwtConstants.SWEEP_TYPE_MANUAL
								+ "'and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}

			if (type.equals("A")) {
				sb
						.append(" and sweep.originalSweepAmt>='" + amountover
								+ "' and sweep.originalSweepAmt<='"
								+ amountunder + "'");
			}
		}
		log.debug(this.getClass().getName() + " -[calculateamount]- Exiting");
		return sb;
	}

	/**
	 * @param StringBuffer
	 * @param status
	 * @param type
	 * @param amountover
	 * @param amountunder
	 *            returns StringBuffer Method used to form sub query to handle
	 *            searched amount
	 */

	private StringBuffer calculateamount1(StringBuffer sb, String status,
			String type, double amountover) {
		/*
		 * variable 'type' holds U/M/A stands for AUTO/MANUAL/ALL variable
		 * 'status' holds N/S/U/B/A stands for
		 * STPNEW/SUBMITTED/AUGHORISED/CANCELLED/ALL
		 */
		log.debug(this.getClass().getName() + " -[calculateamount1]- Entering");
		if (status.equals("N")) {
			if (type.equals("U")) {
				sb.append(" and (sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_NEW
						+ "' or sweep.sweepStatus='S') and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "'and sweep.originalSweepAmt>='" + amountover + "'");
			}

			if (type.equals("M")) {
				sb.append(" and (sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_NEW
						+ "' or sweep.sweepStatus='S') and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_MANUAL
						+ "'and sweep.originalSweepAmt>='" + amountover + "'");
			}

			if (type.equals("A")) {
				sb
						.append(" and (sweep.sweepStatus='"
								+ SwtConstants.SWEEP_STATUS_NEW
								+ "' or sweep.sweepStatus='S') and sweep.originalSweepAmt>='"
								+ amountover + "'");
			}
		} else if (status.equals("S")) {

			if (type.equals("U")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_SUBMIT
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "'and (sweep.submitSweepAmt>='" + amountover
						+ "'or sweep.originalSweepAmt>='" + amountover + "')");
			}

			if (type.equals("M")) {// Not a condition
				sb.append(" and sweep.submitSweepAmt>='" + amountover + "'");
			}

			if (type.equals("A")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_SUBMIT
						+ "'and (sweep.submitSweepAmt>='" + amountover
						+ "'or sweep.originalSweepAmt>='" + amountover + "')");
			}
		} else if (status.equals("U")) {
			if (type.equals("U")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_AUTHORIZE
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "'and (sweep.authorizeSweepAmt>='" + amountover
						+ "'or sweep.originalSweepAmt>='" + amountover + "')");
			}

			if (type.equals("M")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_AUTHORIZE
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_MANUAL
						+ "'and (sweep.authorizeSweepAmt>='" + amountover
						+ "'or sweep.originalSweepAmt>='" + amountover + "')");
			}

			if (type.equals("A")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_AUTHORIZE
						+ "'and (sweep.authorizeSweepAmt>='" + amountover
						+ "'or sweep.originalSweepAmt>='" + amountover + "')");
			}
		} else if (status.equals("B")) {
			if (type.equals("U")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_CANCEL
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "'and sweep.originalSweepAmt>='" + amountover + "'");
			}

			if (type.equals("M")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_CANCEL
						+ "' and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_MANUAL
						+ "'and sweep.originalSweepAmt>='" + amountover + "'");
			}

			if (type.equals("A")) {
				sb.append(" and sweep.sweepStatus='"
						+ SwtConstants.SWEEP_STATUS_CANCEL
						+ "' and sweep.originalSweepAmt>='" + amountover + "'");
			}
		} else {
			if (type.equals("U")) {
				sb.append(" and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_AUTO
						+ "'and sweep.originalSweepAmt>='" + amountover + "'");
			}

			if (type.equals("M")) {
				sb.append(" and sweep.sweepType='"
						+ SwtConstants.SWEEP_TYPE_MANUAL
						+ "'and sweep.originalSweepAmt>='" + amountover + "'");
			}

			if (type.equals("A")) {
				sb.append(" and sweep.originalSweepAmt>='" + amountover + "'");
			}
		}
		log.debug(this.getClass().getName() + " -[calculateamount1]- Exiting");
		return sb;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.SweepSearchDAO#fetchsweepCutOff(java.lang.String,
	 *      java.lang.String, java.lang.String, java.lang.String,
	 *      java.lang.String)
	 */
	public Sweep fetchsweepCutOff(String accountIdCr, String accountIdDr,
			String entityIdCr, String entityIddr, String hostid,
			Long sweepvalue, Sweep sweep, String archiveId) throws SwtException {
		log.debug(this.getClass().getName() + " -[fetchsweepCutOff]- Entering");

		String accountCutoffTime = "from AcctMaintenance acc where acc.id.hostId=?0 and acc.id.entityId=?1 and acc.id.accountId=?2 ";
		java.util.List AccListCr = getHibernateTemplate().find(
				accountCutoffTime,
				new Object[] { hostid, entityIdCr, accountIdCr });
		java.util.List AccListDr = getHibernateTemplate().find(
				accountCutoffTime,
				new Object[] { hostid, entityIddr, accountIdDr });
		AcctMaintenance accountCr = (AcctMaintenance) AccListCr.get(0);
		AcctMaintenance accountDr = (AcctMaintenance) AccListDr.get(0);
		String cutoffCr = accountCr.getCutoff();
		String cutoffDr = accountDr.getCutoff();

		StringTokenizer sttokenCr = null;
		sttokenCr = new StringTokenizer(cutoffCr.trim(), ":");

		String cutoffCrafter = "";

		while (sttokenCr.hasMoreTokens()) {
			cutoffCrafter += sttokenCr.nextToken();
		}
		StringTokenizer sttokenDr = null;
		sttokenDr = new StringTokenizer(cutoffDr.trim(), ":");

		String cutoffDrafter = "";

		while (sttokenDr.hasMoreTokens()) {
			cutoffDrafter += sttokenDr.nextToken();
		}
		AcctMaintenance accountMin = new AcctMaintenance();
		String entity = null;
		String cutoffFinal = null;

		if (Integer.parseInt(cutoffDrafter.trim()) <= Integer
				.parseInt(cutoffCrafter.trim())) {
			accountMin = accountDr;
			entity = accountDr.getId().getEntityId();
			cutoffFinal = accountDr.getCutoff();
		} else {
			accountMin = accountCr;
			entity = accountCr.getId().getEntityId();
			cutoffFinal = accountCr.getCutoff();
		}

		cutoffFinal = cutoffFinal.trim();

		int indexcutoff = cutoffFinal.indexOf(":");
		String hourcutoff = cutoffFinal.substring(0, indexcutoff);
		String minutecutoff = cutoffFinal.substring(indexcutoff + 1,
				cutoffFinal.length());

		Calendar calMinAccCutoffDate = new GregorianCalendar();

		String entityCutoffHQL = "from Entity e where e.id.hostId=?0 and e.id.entityId=?1";

		java.util.List entityList = getHibernateTemplate().find(
				entityCutoffHQL, new Object[] { hostid, entity });
		Entity ent = new Entity();
		ent = (Entity) entityList.get(0);

		String entSerCutoffTime = ent.getEntServerTimeOffSet();
		entSerCutoffTime = entSerCutoffTime.replace('+', '0');
		

		int index = entSerCutoffTime.indexOf(":");
		String hour = entSerCutoffTime.substring(0, index);
		String minute = entSerCutoffTime.substring(index + 1, entSerCutoffTime
				.length());

		if (sweep.getInputDateTime() != null) {
			calMinAccCutoffDate.setTime(sweep.getInputDateTime());
			calMinAccCutoffDate.set(Calendar.HOUR_OF_DAY, Integer
					.parseInt(hourcutoff));
			calMinAccCutoffDate.set(Calendar.MINUTE, Integer
					.parseInt(minutecutoff));

			Calendar calInputDateTime = new GregorianCalendar();
			calInputDateTime.setTime(sweep.getInputDateTime());
			calInputDateTime.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calInputDateTime.add(Calendar.MINUTE, Integer.parseInt(minute));

			if (calMinAccCutoffDate.before(calInputDateTime)) {
				sweep.setGeneratedpostcutoffflg("Y");
			} else {
				sweep.setGeneratedpostcutoffflg("N");
			}
		}

		if (sweep.getAuthDateTime() != null) {
			calMinAccCutoffDate.setTime(sweep.getAuthDateTime());
			calMinAccCutoffDate.set(Calendar.HOUR_OF_DAY, Integer
					.parseInt(hourcutoff));
			calMinAccCutoffDate.set(Calendar.MINUTE, Integer
					.parseInt(minutecutoff));

			Calendar calAuthDateTime = new GregorianCalendar();
			calAuthDateTime.setTime(sweep.getAuthDateTime());
			calAuthDateTime.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calAuthDateTime.add(Calendar.MINUTE, Integer.parseInt(minute));

			if (calMinAccCutoffDate.before(calAuthDateTime)) {
				sweep.setAuthorisedpostcutoffflg("Y");
			} else {
				sweep.setAuthorisedpostcutoffflg("N");
			}
		}

		if (sweep.getSubmitDateTime() != null) {
			calMinAccCutoffDate.setTime(sweep.getSubmitDateTime());
			calMinAccCutoffDate.set(Calendar.HOUR_OF_DAY, Integer
					.parseInt(hourcutoff));
			calMinAccCutoffDate.set(Calendar.MINUTE, Integer
					.parseInt(minutecutoff));

			Calendar calSubmitDateTime = new GregorianCalendar();
			calSubmitDateTime.setTime(sweep.getSubmitDateTime());
			calSubmitDateTime.add(Calendar.HOUR_OF_DAY, Integer.parseInt(hour));
			calSubmitDateTime.add(Calendar.MINUTE, Integer.parseInt(minute));

			if (calMinAccCutoffDate.before(calSubmitDateTime)) {
				sweep.setSubmittedpostcutoffflg("Y");
			} else {
				sweep.setSubmittedpostcutoffflg("N");
			}
		}
		return sweep;
	}
	
	

	public String replaceFieldsInHQLWithArchiveId(String hqlQuery, String hostId, String archiveId) {
		try {
			if (SwtUtil.isEmptyOrNull(archiveId)) {
				return hqlQuery;
			}

			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
			Archive archive = archiveManager.getArchiveById(hostId, archiveId);

			if (archive == null) {
				return hqlQuery;
			}

			return replaceFieldsInHQL(hqlQuery, archive.getDb_link(), archive.getArchiveType(), hostId);
		} catch (Exception e) {
			log.error("Error in replaceFieldsInHQLWithArchiveId: " + e.getMessage());
			return hqlQuery;
		}
	}

	public String replaceFieldsInHQL(String hqlQuery, String dbLink, String archiveType, String hostId) {
	    // Define the field mappings
	    LinkedHashMap<String, String> fieldMappings = new LinkedHashMap<>();
	    fieldMappings.put("\\.id\\.", ".");

	    // Use the new utility method to format table names
	    String sweepTable = SwtUtil.formatTableWithDbLink("P_SWEEP", dbLink, archiveType);
	    String movementTable = SwtUtil.formatTableWithDbLink("P_MOVEMENT", dbLink, archiveType);
	    String accountTable = SwtUtil.formatTableWithDbLink("P_ACCOUNT", dbLink, archiveType);

	    fieldMappings.put(" Sweep", " " + sweepTable);
	    fieldMappings.put(" Movement", " " + movementTable);
	    fieldMappings.put("AcctMaintenance", accountTable);
	    fieldMappings.put("sweepId", "SWEEP_ID");
	    fieldMappings.put("hostId", "HOST_ID");
	    fieldMappings.put("entityIdDr", "ENTITY_ID_CR");
	    fieldMappings.put("sweepGroupId", "SWEEP_GROUP_ID");
	    fieldMappings.put("currencyCode", "CURRENCY_CODE");
	    fieldMappings.put("movementIdCr", "MOVEMENT_ID_CR");
	    fieldMappings.put("movementIdDr", "MOVEMENT_ID_DR");
	    fieldMappings.put("originalSweepAmt", "ORIGINAL_SWEEP_AMT");
	    fieldMappings.put("submitSweepAmt", "SUBMIT_SWEEP_AMT");
	    fieldMappings.put("authorizeSweepAmt", "AUTHORIZE_SWEEP_AMOUNT");
	    fieldMappings.put("sweepType", "SWEEP_TYPE");
	    fieldMappings.put("sweepStatus", "SWEEP_STATUS");
	    fieldMappings.put("inputDateTime", "INPUT_DATE_TIME");
	    fieldMappings.put("authDateTime", "AUTHORIZED_DATE_TIME");
	    fieldMappings.put("inputUser", "INPUT_USER");
	    fieldMappings.put("submitUser", "SUBMIT_USER");
	    fieldMappings.put("authorizedUser", "AUTHORIZED_USER");
	    fieldMappings.put("cancelUser", "CANCEL_USER");
	    fieldMappings.put("accountIdCr", "ACCOUNT_ID_CR");
	    fieldMappings.put("accountIdDr", "ACCOUNT_ID_DR");
	    fieldMappings.put("alignAccountId", "ALIGN_ACCOUNT_ID");
	    fieldMappings.put("valueDate", "VALUE_DATE");
	    fieldMappings.put("updateDate", "UPDATE_DATE");
	    fieldMappings.put("updateUser", "UPDATE_USER");
	    fieldMappings.put("submitDateTime", "SUBMIT_DATE_TIME");
	    fieldMappings.put("cancelDateTime", "CANCEL_DATE_TIME");
	    fieldMappings.put("entityIdCr", "ENTITY_ID_CR");
	    fieldMappings.put("entityIdDr", "ENTITY_ID_DR");
	    fieldMappings.put("settleMethodCR", "SETTL_METHOD_CR");
	    fieldMappings.put("settleMethodDR", "SETTL_METHOD_DR");
	    fieldMappings.put("bookCodeCR", "BOOKCODE_CR");
	    fieldMappings.put("bookCodeDR", "BOOKCODE_DR");
	    fieldMappings.put("additionalReference", "ADDITIONAL_REFERENCE");
	    fieldMappings.put("targetBalance", "TARGET_BALANCE");
	    fieldMappings.put("targetBalanceType", "TARGET_BALANCE_TYPE");
	    fieldMappings.put("minAmount", "MIN_AMOUNT");
	    fieldMappings.put("accountIdOrigin", "ACCOUNT_ID_ORIGIN");
	    fieldMappings.put("entityIdOrigin", "ENTITY_ID_ORIGIN");
	    fieldMappings.put("sweepFromBalanceTypeCr", "SWEEP_FROM_BALANCE_TYPE_CR");
	    fieldMappings.put("sweepFromBalanceTypeDr", "SWEEP_FROM_BALANCE_TYPE_DR");
	    fieldMappings.put("targetBalanceTypId", "TARGET_BALANCE_TYPE_ID");
	    fieldMappings.put("acctNewDrInternal", "NEW_INTERNAL_DR_FORMAT");
	    fieldMappings.put("acctNewDrExternal", "NEW_EXTERNAL_DR_FORMAT");
	    fieldMappings.put("acctNewCrInternal", "NEW_INTERNAL_CR_FORMAT");
	    fieldMappings.put("acctNewCrExternal", "NEW_EXTERNAL_CR_FORMAT");
	    fieldMappings.put("accountId", "ACCOUNT_ID");
	    
	    fieldMappings.put("entityId", "ENTITY_ID");
	    fieldMappings.put("movementId", "MOVEMENT_ID");
	    // Replace the fields in the query using regular expressions
	    String replacedQuery = hqlQuery;
	    for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
	        String fieldRegex =  entry.getKey() ;
	        replacedQuery = replacedQuery.replaceAll(fieldRegex, entry.getValue());
	    }


		// Replace Hibernate-style parameters with SQL-style parameters
		replacedQuery = replacedQuery.replaceAll("\\?\\d+", "?");


		return replacedQuery;
	}
}
